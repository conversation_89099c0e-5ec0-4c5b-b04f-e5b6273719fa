import random
address_range = [[0x90000000,0x9fffffff],
                 [0xa0000000,0xa02fffff],
                 [0xa0300000,0xa05fffff],
                 [0xa0600000,0xa08fffff],
                 [0xa0900000,0xa0bfffff],
                 [0xa0c00000,0xa0efffff],
                 [0xa0f00000,0xa0ffffff],
                 [0xa1000000,0xaf3fffff],
                 [0xaf400000,0xaf400dff],
                 [0xaf400e00,0xaf400fff],
                 [0xaf401000,0xaf405fff],
                 [0xaf406000,0xafbfffff],
                 [0xafc00000,0xafc1ffff],
                 [0xafc20000,0xafffffff],
                 [0xb0000000,0xbfffffff],
                 [0xc0000000,0xcfffffff],
                 [0xd0000000,0xdfffffff]]
    
samples_per_range = 100  # 每个范围采样100个地址

try:
    for start, end in address_range:
        # 确保起始和结束地址被采样到
        addresses = [start, end-256]
        
        # 计算剩余需要采样的数量
        remaining_samples = samples_per_range - 2
        
        # 在范围内随机生成剩余的地址
        range_size = end - start
        if remaining_samples > 0:
            step = range_size // (remaining_samples + 1)  # 计算采样间隔
            for i in range(remaining_samples):
                # 生成一个随机偏移量，确保不会与已有地址重复
                offset = random.randint(1, step - 1)
                addr = start + (i + 1) * step + offset
                addr = min(addr, end)  # 确保不超过范围
                addresses.append(addr)
        print(hex(start),'-',hex(end),"ADDRESSES:")
        for each in addresses:
            print(hex(each))
        # 对每个采样的地址进行测试
        for addr in addresses:
            memoryAddress = hex(addr)[2:].zfill(8)  # 转换为8位16进制字符串

except KeyboardInterrupt:
    pass