import SoapySDR
import numpy as np
import time
from SoapySDR import SOAPY_SDR_TX, SOAPY_SDR_CF32

# === 参数 ===
CENTER_FREQ = 433.92e6       # TPMS 通常频率
SAMPLE_RATE = 2e6
FSK_DEV = 50e3               # FSK 频偏 ±50kHz
BITRATE = 20000              # 比特率
TX_GAIN = 25                 # 发射增益
TPMS_HEX = "00000000014d0414de0104a6012a06"

# === 将十六进制转为比特串 ===
def hex_to_bits(hex_str):
    return ''.join(f"{int(hex_str[i:i+2], 16):08b}" for i in range(0, len(hex_str), 2))

bitstream = hex_to_bits(TPMS_HEX)
print("发送比特流:", bitstream)

# === 生成 FSK 波形 ===
samples_per_bit = int(SAMPLE_RATE / BITRATE)
t = np.arange(samples_per_bit) / SAMPLE_RATE
iq_data = np.array([], dtype=np.complex64)

for bit in bitstream:
    freq = FSK_DEV if bit == '1' else -FSK_DEV
    wave = np.exp(2j * np.pi * freq * t)
    iq_data = np.concatenate((iq_data, wave.astype(np.complex64)))

# === SDR 设置 ===
sdr = SoapySDR.Device(dict(driver="hackrf"))
sdr.setSampleRate(SOAPY_SDR_TX, 0, SAMPLE_RATE)
sdr.setFrequency(SOAPY_SDR_TX, 0, CENTER_FREQ)
sdr.setGain(SOAPY_SDR_TX, 0, TX_GAIN)

# 启动流
tx_stream = sdr.setupStream(SOAPY_SDR_TX, SOAPY_SDR_CF32)
sdr.activateStream(tx_stream)

# 循环发射
print("开始循环发射 TPMS FSK 信号，Ctrl+C 停止...")
try:
    while True:
        sr = sdr.writeStream(tx_stream, [iq_data], len(iq_data))
        if sr.ret != len(iq_data):
            print("⚠️ 发送失败:", sr)
except KeyboardInterrupt:
    print("停止发射。")

sdr.deactivateStream(tx_stream)
sdr.closeStream(tx_stream)

