import can
import secrets
import time
import random
# 设置CAN接口通道名称和类型
CHANNEL = 'can0'       # 根据你的设备实际情况修改，如 socketcan 用 'can0'
BUSTYPE = 'socketcan'  # 如果使用PCAN请改成 'pcan'，Kvaser使用 'kvaser'

# 初始化 CAN 总线对象
bus = can.interface.Bus(channel=CHANNEL, bustype=BUSTYPE, fd=True)

def generate_random_data():
    # 生成最多64字节（128位十六进制字符）
    data_length = secrets.randbelow(64) + 1  # 避免长度为0
    hex_data = secrets.token_hex(data_length)
    return bytes.fromhex(hex_data[:data_length * 2])

def send_fuzz_frames():
    while True:
    # for _ in range(10):
        data = generate_random_data()
        for can_id in range(0x000, 0x800):  # 标准ID范围 0x000 ~ 0x7FF
            
            msg = can.Message(
                arbitration_id=can_id,
                data=data,
                is_fd=True,
                is_extended_id=False
            )
            try:
                bus.send(msg)
                print(f"Sent CAN FD frame - ID: {hex(can_id)} Data: {data.hex()}")
            except can.CanError as e:
                print(f"Failed to send message with ID {hex(can_id)}: {e}")
            time.sleep(random.uniform(0, 2))  # 稍微延迟，避免总线过载

if __name__ == "__main__":
    try:
        send_fuzz_frames()
    except KeyboardInterrupt:
        print("Fuzzing interrupted by user.")
