import can
import time

# 创建 CAN 总线接口
bus = can.interface.Bus(channel='can0', bustype='socketcan')

# 发送 CAN 报文的函数
def send_can_messages(start_id, end_id):
    for can_id in range(start_id, end_id + 1):
        msg = can.Message(arbitration_id=can_id, data=[0x02, 0x10, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00], is_extended_id=True)
        try:
            bus.send(msg)
            time.sleep(0.001)
            print(f"Message sent on {bus.channel_info}: {msg}")
        except can.CanError:
            print(f"Message NOT sent on {bus.channel_info}")

# 发送从 0x11111111 到 0x22222222 的扩展报文
send_can_messages(0x11111111, 0xffffffff)
