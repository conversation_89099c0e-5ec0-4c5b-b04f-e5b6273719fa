import can
import binascii

# 设置CAN接口（根据你实际使用的接口调整）
bus = can.interface.Bus(channel='can0', bustype='socketcan')

# 根据接收到的客户端请求进行响应的函数
def process_request(msg_rx):
    # 获取客户端发送的ID和数据
    client_id = msg_rx.arbitration_id
    data_received = msg_rx.data
    
    print(f"接收到来自 {hex(client_id)} 的数据: {data_received.hex()}")
    
    # 根据不同的请求数据返回不同的响应
    if client_id == 0x18E06188:
        if bytes([0x22, 0xF1, 0x19]) in data_received:
            response = bytes([0x05,0x62, 0xF1, 0x19, 0x20, 0x20])  # 响应 62F1192020
        elif bytes([0x22, 0xF1, 0x50]) in data_received:
            response = bytes([0x06, 0x62, 0xF1, 0x50, 0x00, 0x00, 0x13])  # 响应 62F150000013
        elif  bytes([0x2E, 0xF1, 0x19, 0x20, 0x20]) in data_received:
            response = bytes([0x03, 0x6E, 0xF1, 0x19])  # 响应 6EF119
        elif bytes([0x2E, 0xF1, 0x50, 0x00, 0x00, 0x13]) in data_received:
            response = bytes([0x03, 0x7F, 0x2E, 0x31])  # 响应 7F2E31
        else:
            response = None  # 未知请求，发送错误代码
        # 创建响应报文
        if response:
            msg_tx = can.Message(arbitration_id=0x18E06180, data=response, is_extended_id=True)
            bus.send(msg_tx)
            print(f"发送响应: {response.hex()} 到 ID: {hex(0x18E06180)}")
    else:
        print(f"未知客户端 ID: {hex(client_id)}，忽略消息")

# 服务端监听并处理消息的主循环
def server_loop():
    print("服务端已启动，等待客户端请求...")
    
    while True:
        # 接收客户端发来的报文
        msg_rx = bus.recv(timeout=1.0)  # 设置超时1秒
        if msg_rx:
            process_request(msg_rx)
        else:
            print("没有接收到消息，继续等待...")

# 启动服务端监听
if __name__ == '__main__':
    server_loop()

