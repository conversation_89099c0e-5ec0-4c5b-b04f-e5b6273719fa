import cmd2
import sys
import os
from utils.scanUDSid import *
import getopt
import logging
import binascii
from scapy.contrib.cansocket import CANSocket
from scapy.config import conf
import json
import time
from scapy.layers.can import CAN
from scapy.contrib.isotp import ISOTPSocket
from scapy.contrib.isotp import ISOTPNativeSocket
from scapy.contrib.automotive.uds_scan import * 
from scapy.layers.can import *
from scapy.main import load_contrib
from reprint import output
conf.contribs['ISOTP'] = {'use-can-isotp-kernel-module': True}
# conf.contribs['CANSocket'] = {'use-python-can': False}
load_contrib('automotive.uds')
load_contrib('isotp')

logging.basicConfig(filename='can.log', level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')




logger = logging.getLogger(__name__)
globalTimeout = 0.2

negativeResponseCodes = {
    0x00: "POSITIVE_RESPONSE",
    0x10: "GENERAL_REJECT",
    0x11: "SERVICE_NOT_SUPPORTED",
    0x12: "SUB_FUNCTION_NOT_SUPPORTED",
    0x13: "INCORRECT_MESSAGE_LENGTH_OR_INVALID_FORMAT",
    0x14: "RESPONSE_TOO_LONG",
    0x21: "BUSY_REPEAT_REQUEST",
    0x22: "CONDITIONS_NOT_CORRECT",
    0x24: "REQUEST_SEQUENCE_ERROR",
    0x25: "NO_RESPONSE_FROM_SUBNET_COMPONENT",
    0x26: "FAILURE_PREVENTS_EXECUTION_OF_REQUESTED_ACTION",
    0x31: "REQUEST_OUT_OF_RANGE",
    0x33: "SECURITY_ACCESS_DENIED",
    0x35: "INVALID_KEY",
    0x36: "EXCEEDED_NUMBER_OF_ATTEMPTS",
    0x37: "REQUIRED_TIME_DELAY_NOT_EXPIRED",
    0x70: "UPLOAD_DOWNLOAD_NOT_ACCEPTED",
    0x71: "TRANSFER_DATA_SUSPENDED",
    0x72: "GENERAL_PROGRAMMING_FAILURE",
    0x73: "WRONG_BLOCK_SEQUENCE_COUNTER",
    0x78: "REQUEST_CORRECTLY_RECEIVED_RESPONSE_PENDING",
    0x7E: "SUB_FUNCTION_NOT_SUPPORTED_IN_ACTIVE_SESSION",
    0x7F: "SERVICE_NOT_SUPPORTED_IN_ACTIVE_SESSION",
    0x81: "RPM_TOO_HIGH",
    0x82: "RPM_TOO_LOW",
    0x83: "ENGINE_IS_RUNNING",
    0x84: "ENGINE_IS_NOT_RUNNING",
    0x85: "ENGINE_RUN_TIME_TOO_LOW",
    0x86: "TEMPERATURE_TOO_HIGH",
    0x87: "TEMPERATURE_TOO_LOW",
    0x88: "VEHICLE_SPEED_TOO_HIGH",
    0x89: "VEHICLE_SPEED_TOO_LOW",
    0x8A: "THROTTLE_PEDAL_TOO_HIGH",
    0x8B: "THROTTLE_PEDAL_TOO_LOW",
    0x8C: "TRANSMISSION_RANGE_NOT_IN_NEUTRAL",
    0x8D: "TRANSMISSION_RANGE_NOT_IN_GEAR",
    0x8F: "BRAKE_SWITCHES_NOT_CLOSED",
    0x90: "SHIFT_LEVER_NOT_IN_PARK",
    0x91: "TORQUE_CONVERTER_CLUTCH_LOCKED",
    0x92: "VOLTAGE_TOO_HIGH",
    0x93: "VOLTAGE_TOO_LOW"
}

UDS_SERVICE_NAMES = {
    0x10: "DIAGNOSTIC_SESSION_CONTROL",
    0x11: "ECU_RESET",
    0x14: "CLEAR_DIAGNOSTIC_INFORMATION",
    0x19: "READ_DTC_INFORMATION",
    0x20: "RETURN_TO_NORMAL",
    0x22: "READ_DATA_BY_IDENTIFIER",
    0x23: "READ_MEMORY_BY_ADDRESS",
    0x24: "READ_SCALING_DATA_BY_IDENTIFIER",
    0x27: "SECURITY_ACCESS",
    0x28: "COMMUNICATION_CONTROL",
    0x2A: "READ_DATA_BY_PERIODIC_IDENTIFIER",
    0x2C: "DYNAMICALLY_DEFINE_DATA_IDENTIFIER",
    0x2D: "DEFINE_PID_BY_MEMORY_ADDRESS",
    0x2E: "WRITE_DATA_BY_IDENTIFIER",
    0x2F: "INPUT_OUTPUT_CONTROL_BY_IDENTIFIER",
    0x31: "ROUTINE_CONTROL",
    0x34: "REQUEST_DOWNLOAD",
    0x35: "REQUEST_UPLOAD",
    0x36: "TRANSFER_DATA",
    0x37: "REQUEST_TRANSFER_EXIT",
    0x38: "REQUEST_FILE_TRANSFER",
    0x3D: "WRITE_MEMORY_BY_ADDRESS",
    0x3E: "TESTER_PRESENT",
    0x7F: "NEGATIVE_RESPONSE",
    0x83: "ACCESS_TIMING_PARAMETER",
    0x84: "SECURED_DATA_TRANSMISSION",
    0x85: "CONTROL_DTC_SETTING",
    0x86: "RESPONSE_ON_EVENT",
    0x87: "LINK_CONTROL"
}

RED = "\033[1;31m"
BLUE = "\033[1;34m"
CYAN = "\033[1;36m"
WHITE = "\033[1;37m"
YELLOW = "\033[1;33m"
GREEN = "\033[1;32m"
RESET = "\033[1;0m"
BOLD = "\033[;1m"
REVERSE = "\033[;7m"


def Bitmask(cid, sid):
    return cid ^ sid ^ 0x7ff

class MyApp(cmd2.Cmd):
    def __init__(self):
        cmd2.Cmd.__init__(self)
        del cmd2.Cmd.do_edit
        del cmd2.Cmd.do_history
        del cmd2.Cmd.do_macro
        del cmd2.Cmd.do_run_pyscript
        del cmd2.Cmd.do_run_script
        del cmd2.Cmd.do_set
        del cmd2.Cmd.do_shell
        del cmd2.Cmd.do_shortcuts
        del cmd2.Cmd.do_alias
        self.interface = 'can0'
        self.fd = False
        self.key = None
        self.bs = None
        self.ext = False
        self.sourcelist = []
        self.destinationlist = []
        self.socketPool = []

        if not os.path.exists('data'):
            os.makedirs('data')


        self.prompt = CYAN + 'CAN➤ ' + RESET
        self.aliases.update({
            'q': 'quit',
            'h': 'help'
        })
    def do_connect(self, args):
        '''
        Create socket use source address and target address.
        Usage: 
            connect
        '''
        if len(self.sourcelist) == 0 or len(self.destinationlist) == 0:
            print(RED + '[!] Please set source and target address first.' + RESET)
            return False
        for i in range(len(self.sourcelist)):
            if self.fd:
                self.socketPool.append(ISOTPNativeSocket('can0',tx_id=self.sourcelist[i], rx_id=self.destinationlist[i] ,padding=True,fd=True,basecls=ISOTP))
            else:
                self.socketPool.append(ISOTPSocket(CANSocket(bustype='socketcan',fd=self.fd,channel=self.interface,can_filters=[{'can_id': self.sourcelist[i],'can_mask': Bitmask(self.sourcelist[i],self.destinationlist[i])},
                {'can_id': self.destinationlist[i],'can_mask': Bitmask(self.sourcelist[i],self.destinationlist[i])}]), tx_id=self.sourcelist[i], rx_id=self.destinationlist[i],padding=True))
        

    def do_udsid(self, args):
        '''
        Scan Diagnostic ID.
        You can set scan range from begin(default 0x0) to end(default 0x7ff).
        You can set sniffer timeout(default 100ms).
        The results will be automatically set to the source and target.
        
        Usage: 
            udsid
            udsid [-b --begin] [-e --end] [-t --timeout]
        '''
        begin = 0x0
        end = 0x7ff
        timeout = 100
        args = args.split(' ')
        try:
            opts = getopt.getopt(args, "b:e:t:", ["begin=", "end=","timeout="])
            for opt, arg in opts[0]:
                if opt in ("-b", "--begin"):
                    begin = int(arg, 16)
                elif opt in ("-e", "--end"):
                    end = int(arg, 16)
                elif opt in ("-t", "--timeout"):
                    timeout = int(arg)
        except getopt.GetoptError as msg:
            self.do_help('udsid')
            print("ERROR:", msg)
            return False
        
        if (begin > end):
            print(RED + "[!] Argument 'begin' must < 'end'"+ RESET)
            return False
        
        print('[*] Scanning UDS ID from 0x%x to 0x%x...' % (begin, end))
        source, target = scanner_uds_id(self.interface,self.fd,self.ext,begin, end,timeout)
        if len(source) > 0:
            with open('./data/udsid.txt', 'w') as file:
                for item1, item2 in zip(source, target):
                    file.write(f"{item1} {item2}\n")
            print("Result: ")
            for i in range(len(source)):
                print("Send:\t\t",source[i])
                print("Received:\t",target[i],'\n')
            
            self.sourcelist = [int(x,16) for x in source]
            self.destinationlist = [int(x,16) for x in target]
            
            print('[*] UDS ID saved in ./data/udsid.txt')
            print('[*] Done!')
        else:
            print(RED+ "\n[!] No UDS ID found." + RESET)
        
    def do_set(self,args):
        '''
        You can manually set some values.
        Such as: target_address、source_address、key、timeout(default 0.2s),
                blocksize、file(set target_address、source_address from file),
                interface(default can0)、canfd(default can)、extended_can_id(default False)
        
        Usage: set [-t --target] [-s --source] [-k --key] [-T --timeout]
                   [-b --bs] [-f --file] [-i --interface] [--fd] [--ext]
        '''
        global globalTimeout
        args = args.split(' ')
        try:
            options = getopt.getopt(
                    args,'t:s:k:T:b:f:i:d:',
                    ['target=', 'source=','key=','timeout=','bs=','file=','interface=','debug=','fd','ext'])
            for opt, arg in options[0]:
                if opt in ('-s', '--source'):
                    self.sourcelist = [int(x,16) for x in arg.split(',')]
                elif opt in ('-t', '--target'):
                    self.destinationlist = [int(x,16) for x in arg.split(',')]
                elif opt in ('-k', '--key'):
                    self.key = arg
                elif opt in ('-b', '--bs'):
                    self.bs = int(arg)-2
                elif opt in ('-T','--timeout'):
                    globalTimeout = float(arg)
                elif opt in ('-f', '--file'):
                    with open(arg,'r') as f:
                        data = f.read()
                        self.sourcelist = [int(x.split()[0],16) for x in data.strip().split('\n')]
                        self.destinationlist = [int(x.split()[1],16) for x in data.strip().split('\n')]
                elif opt in ('--fd'):
                    self.fd = True
                elif opt in ('--ext'):
                    self.ext = True
                elif opt in ('-i', '--interface'):
                    self.interface = arg
                elif opt in ('-d','--debug'):
                    self.debug = True
                    # pass
        except getopt.GetoptError as msg:
            self.do_help('set')
            print("ERROR:", msg)
        
    def do_show_values(self,args):
        print("\nSource:",[hex(x) for x in self.sourcelist])
        print("Target:",[hex(x) for x in self.destinationlist])
        print("Interface:",self.interface)
        print("CAN FD:",self.fd)
        print("Extended CAN:",self.ext)
        print("Timeout:",globalTimeout)
        print("Key:",self.key)
        print("Block size:",self.bs)

if __name__ == '__main__':
    MyApp().cmdloop()