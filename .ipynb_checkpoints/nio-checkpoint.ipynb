{"cells": [{"cell_type": "code", "execution_count": 1, "id": "dee6bde5-1e78-47a8-aa4f-fbdf8bc56c83", "metadata": {"tags": []}, "outputs": [], "source": ["import cmd2\n", "import os\n", "import logging\n", "import binascii\n", "from scapy.contrib.cansocket import CANSocket\n", "from scapy.config import conf\n", "from scapy.all import raw\n", "import argparse\n", "import json\n", "import time\n", "from scapy.layers.can import CAN\n", "from scapy.contrib.isotp import ISOTPSocket\n", "from scapy.contrib.isotp import ISOTPNativeSocket\n", "from scapy.contrib.automotive.uds_scan import * \n", "from scapy.layers.can import *\n", "from scapy.main import load_contrib\n", "import signal\n", "import threading\n", "import subprocess\n", "\n", "\n", "\n", "conf.contribs['ISOTP'] = {'use-can-isotp-kernel-module': True}\n", "conf.contribs['CANSocket'] = {'use-python-can': True}\n", "load_contrib('automotive.uds')\n", "load_contrib('isotp')\n", "\n", "def insert_spaces(s):\n", "    s = s.upper()\n", "    return ' '.join(s[i:i+2] for i in range(0, len(s), 2))"]}, {"cell_type": "code", "execution_count": 3, "id": "e005b903-82f8-4442-b6a6-3a7f55b3e4b1", "metadata": {"tags": []}, "outputs": [], "source": ["# tx, rx = 0x670,0x6f0 # BTA1\n", "# tx, rx = 0x63e,0x6be # CCU\n", "# tx, rx = 0x615,0x695 # ACM CAN\n", "# tx, rx = 0x60c,0x68c # BMS\n", "# tx, rx = 0x616,0x696 # BCU\n", "tx, rx = 0x734,0x7b4 # EPS\n", "# tx, rx = 0x60f,0x68f # EDS\n", "# tx, rx = 0x611,0x691 # EVCC\n", "# tx, rx = 0x63a,0x6ba # LBM\n", "# tx, rx = 0x612,0x692 # BCM\n", "# tx, rx = 0x61e,0x69e # RAD_FC\n", "# tx, rx = 0x678,0x6f8 # RAD_SR\n", "# tx, rx = 0x633,0x6b3 # IC\n", "# tx, rx = 0x636,0x6b6 # ICS\n", "# tx, rx = 0x613,0x693 # OBCM\n", "# tx, rx = 0x672,0x6f2 # BTA3\n", "# tx, rx = 0x674,0x6f4 # BTA5\n", "\n", "def crc8(data, length):\n", "    crc = 0xff\n", "    for i in range(length):\n", "        crc ^= data[i]\n", "        for b in range(8):\n", "            if crc & 0x80:\n", "                crc = (crc << 1) ^ 0x1d\n", "            else:\n", "                crc = crc << 1\n", "    return ~crc & 0xFF\n", "\n", "def ASAP1A__CCP_ComputeKeyFromSeed(seed_hex,a,b):\n", "    \n", "    seed = []\n", "    key = [0] * 4\n", "    for i in range(4):\n", "        seed.append(int(seed_hex[i*2:i*2+2],16))\n", "    seed.append(a)\n", "    seed.append(b)\n", "\n", "    seedLength = 6\n", "    buf_byte = [0] * seedLength\n", "    crc_byte = [0] * 7\n", "\n", "    buf_byte[0] = seed[0]\n", "    buf_byte[1] = seed[1]\n", "    buf_byte[2] = seed[2]\n", "    buf_byte[3] = seed[3]\n", "    buf_byte[4] = seed[4]\n", "    buf_byte[5] = seed[5]\n", "\n", "    crc_byte[0] = crc8(buf_byte, seedLength)\n", "\n", "    buf_byte[0] = crc_byte[0]\n", "    crc_byte[1] = crc8(buf_byte, seedLength)\n", "\n", "    buf_byte[0] = seed[0]\n", "    buf_byte[1] = crc_byte[1]\n", "    crc_byte[2] = crc8(buf_byte, seedLength)\n", "\n", "    buf_byte[1] = seed[1]\n", "    buf_byte[2] = crc_byte[2]\n", "    crc_byte[3] = crc8(buf_byte, seedLength)\n", "\n", "    buf_byte[2] = seed[2]\n", "    buf_byte[3] = crc_byte[3]\n", "    crc_byte[4] = crc8(buf_byte, seedLength)\n", "\n", "    buf_byte[3] = seed[3]\n", "    buf_byte[4] = crc_byte[4]\n", "    crc_byte[5] = crc8(buf_byte, seedLength)\n", "\n", "    buf_byte[4] = seed[4]\n", "    buf_byte[5] = crc_byte[5]\n", "    crc_byte[6] = crc8(buf_byte, seedLength)\n", "\n", "    if crc_byte[3] == 0 and crc_byte[4] == 0 and crc_byte[5] == 0 and crc_byte[6] == 0:\n", "        key[0] = crc_byte[1]\n", "        key[1] = crc_byte[2]\n", "        key[2] = crc_byte[3]\n", "        key[3] = crc_byte[4]\n", "    else:\n", "        key[0] = crc_byte[3]\n", "        key[1] = crc_byte[4]\n", "        key[2] = crc_byte[5]\n", "        key[3] = crc_byte[6]\n", "    return ''.join(hex(i)[2:].z<PERSON>(2) for i in key)\n", "\n", "\n", "isoTpSocket = ISOTPNativeSocket('can0', tx_id=tx, rx_id=rx,padding=True,fd=False)\n", "# isoTpSocket = ISOTPNativeSocket('can0', tx_id=tx, rx_id=rx,padding=True,fd=True)"]}, {"cell_type": "code", "execution_count": 4, "id": "3fab4f56-fc67-40f2-921e-d1dfd2f34ee9", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Begin emission:\n", "Finished sending 1 packets.\n", "*\n", "Received 1 packets, got 1 answers, remaining 0 packets\n", "Begin emission:\n", "Finished sending 1 packets.\n", "*\n", "Received 1 packets, got 1 answers, remaining 0 packets\n"]}, {"ename": "NameError", "evalue": "name 'each' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[4], line 9\u001b[0m\n\u001b[1;32m      7\u001b[0m pkg \u001b[38;5;241m=\u001b[39m isoTpSocket\u001b[38;5;241m.\u001b[39msr1(isoTpMessage)\n\u001b[1;32m      8\u001b[0m udsdata \u001b[38;5;241m=\u001b[39m UDS(pkg\u001b[38;5;241m.\u001b[39mdata)\n\u001b[0;32m----> 9\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mSend: \u001b[39m\u001b[38;5;124m'\u001b[39m,insert_spaces(\u001b[43meach\u001b[49m))\n\u001b[1;32m     10\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mRecv: \u001b[39m\u001b[38;5;124m'\u001b[39m,insert_spaces(binascii\u001b[38;5;241m.\u001b[39mb2a_hex(pkg\u001b[38;5;241m.\u001b[39mdata)\u001b[38;5;241m.\u001b[39mdecode(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mutf-8\u001b[39m\u001b[38;5;124m'\u001b[39m)))\n", "\u001b[0;31mNameError\u001b[0m: name 'each' is not defined"]}], "source": ["isoTpMessage = ISOTP(binascii.a2b_hex('1003'))\n", "pkg = isoTpSocket.sr1(isoTpMessage)\n", "base = '31010203'\n", "for i in range(0,10000):\n", "    base += '1'*i*2\n", "    isoTpMessage = ISOTP(binascii.a2b_hex(base))\n", "    pkg = isoTpSocket.sr1(isoTpMessage)\n", "    udsdata = UDS(pkg.data)\n", "    print('Send: ',insert_spaces(base))\n", "    print('Recv: ',insert_spaces(binascii.b2a_hex(pkg.data).decode('utf-8')))"]}, {"cell_type": "markdown", "id": "a06877a7-15c1-48fa-87cd-04ae777d08af", "metadata": {"tags": []}, "source": ["# 27锁定测试\n", "## L2锁定测试"]}, {"cell_type": "code", "execution_count": 3, "id": "f53e174b-0ba6-4b48-859f-30f27f417ecf", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Begin emission:\n", "Finished sending 1 packets.\n", "*\n", "Received 1 packets, got 1 answers, remaining 0 packets\n"]}], "source": ["isoTpMessage = ISOTP(binascii.a2b_hex('1003'))\n", "pkg = isoTpSocket.sr1(isoTpMessage)"]}, {"cell_type": "code", "execution_count": 3, "id": "1ae47660-bd49-4239-abd6-95b9dbb67c2e", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Send:  10 03\n", "Recv:  50 03 00 32 00 C8\n", "Send:  27 03\n", "Recv:  7F 27 78\n", "Recv:  67 03 2D 31 C5 A2\n", "Send:  27 04 11 11 11 11\n", "Recv:  7F 27 78\n", "Recv:  7F 27 35\n", "Send:  27 03\n", "Recv:  7F 27 78\n", "Recv:  67 03 12 74 1B B0\n", "Send:  27 04 11 11 11 11\n", "Recv:  7F 27 78\n", "Recv:  7F 27 35\n", "Send:  27 03\n", "Recv:  7F 27 78\n", "Recv:  67 03 1A 02 0F A3\n", "Send:  27 04 11 11 11 11\n", "Recv:  7F 27 78\n", "Recv:  7F 27 36\n", "Send:  27 03\n", "Recv:  7F 27 37\n", "Send:  27 04 11 11 11 11\n", "Recv:  7F 27 24\n"]}], "source": ["sendmsg = ['1003','2703','270411111111','2703','270411111111','2703','270411111111','2703','270411111111',]\n", "# sendmsg = ['1003']\n", "for each in sendmsg:\n", "    isoTpMessage = ISOTP(binascii.a2b_hex(each))\n", "    pkg = isoTpSocket.sr1(isoTpMessage,verbose=0,timeout=5)\n", "    udsdata = UDS(pkg.data)\n", "    print('Send: ',insert_spaces(each))\n", "    print('Recv: ',insert_spaces(binascii.b2a_hex(pkg.data).decode('utf-8')))\n", "    \n", "    while udsdata.service == 0x7f and udsdata.negativeResponseCode == 0x78:\n", "        pkg = isoTpSocket.recv_raw()\n", "        print('Recv: ',insert_spaces(binascii.b2a_hex(pkg[1]).decode('utf-8')))\n", "        udsdata = UDS(pkg[1])"]}, {"cell_type": "markdown", "id": "953d9a18-5d1f-4597-89d0-90a1c5a16932", "metadata": {}, "source": ["## L4锁定测试"]}, {"cell_type": "code", "execution_count": 3, "id": "95033654-05dc-496a-bcff-020792ac3518", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Send:  10 03\n", "Recv:  50 03 00 32 00 C8\n", "Send:  10 02\n", "Recv:  7F 10 22\n", "Send:  27 07\n", "Recv:  7F 27 12\n", "Send:  27 08 11 11 11 11\n", "Recv:  7F 27 12\n", "Send:  27 07\n", "Recv:  7F 27 12\n", "Send:  27 08 11 11 11 11\n", "Recv:  7F 27 12\n", "Send:  27 07\n", "Recv:  7F 27 12\n", "Send:  27 08 11 11 11 11\n", "Recv:  7F 27 12\n", "Send:  27 07\n", "Recv:  7F 27 12\n", "Send:  27 08 11 11 11 11\n", "Recv:  7F 27 12\n"]}], "source": ["sendmsg = ['1003','1002','2707','270811111111','2707','270811111111','2707','270811111111','2707','270811111111']\n", "for each in sendmsg:\n", "    isoTpMessage = ISOTP(binascii.a2b_hex(each))\n", "    pkg = isoTpSocket.sr1(isoTpMessage,verbose=0)\n", "    udsdata = UDS(pkg.data)\n", "    print('Send: ',insert_spaces(each))\n", "    print('Recv: ',insert_spaces(binascii.b2a_hex(pkg.data).decode('utf-8')))\n", "    \n", "    while udsdata.service == 0x7f and udsdata.negativeResponseCode == 0x78:\n", "        pkg = isoTpSocket.recv_raw()\n", "        print('Recv: ',insert_spaces(binascii.b2a_hex(pkg[1]).decode('utf-8')))\n", "        udsdata = UDS(pkg[1])"]}, {"cell_type": "markdown", "id": "d2a52624-0f73-41f0-adef-95b002a3cf88", "metadata": {}, "source": ["## 31服务"]}, {"cell_type": "code", "execution_count": 3, "id": "2189617d-60a2-459d-9765-2769a9199105", "metadata": {"tags": []}, "outputs": [], "source": ["import itertools\n", "scan_range = range(0x3000,0x4000)\n", "sendrcData = ['00']\n", "output_list = [ UDS() / UDS_RC(routineControlType=0x01,routineIdentifier=data_id) / Raw(load = binascii.a2b_hex(x_data)) for data_id, x_data in itertools.product(scan_range, sendrcData)]"]}, {"cell_type": "code", "execution_count": 4, "id": "c0d27cea-618d-492c-93e7-db258199de43", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Send:  10 03\n", "Recv:  50 03 00 32 00 C8\n", "Send:  31 01 30 00 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 01 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 02 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 03 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 04 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 05 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 06 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 07 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 08 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 09 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 0A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 0B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 0C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 0D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 0E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 0F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 10 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 11 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 12 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 13 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 14 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 15 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 16 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 17 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 18 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 19 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 1A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 1B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 1C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 1D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 1E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 1F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 20 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 21 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 22 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 23 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 24 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 25 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 26 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 27 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 28 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 29 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 2A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 2B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 2C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 2D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 2E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 2F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 30 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 31 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 32 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 33 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 34 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 35 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 36 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 37 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 38 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 39 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 3A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 3B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 3C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 3D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 3E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 3F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 40 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 41 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 42 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 43 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 44 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 45 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 46 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 47 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 48 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 49 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 4A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 4B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 4C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 4D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 4E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 4F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 50 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 51 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 52 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 53 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 54 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 55 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 56 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 57 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 58 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 59 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 5A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 5B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 5C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 5D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 5E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 5F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 60 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 61 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 62 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 63 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 64 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 65 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 66 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 67 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 68 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 69 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 6A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 6B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 6C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 6D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 6E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 6F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 70 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 71 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 72 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 73 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 74 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 75 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 76 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 77 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 78 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 79 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 7A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 7B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 7C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 7D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 7E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 7F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 80 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 81 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 82 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 83 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 84 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 85 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 86 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 87 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 88 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 89 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 8A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 8B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 8C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 8D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 8E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 8F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 90 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 91 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 92 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 93 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 94 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 95 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 96 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 97 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 98 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 99 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 9A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 9B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 9C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 9D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 9E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 9F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 A0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 A1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 A2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 A3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 A4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 A5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 A6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 A7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 A8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 A9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 AA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 AB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 AC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 AD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 AE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 AF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 B0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 B1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 B2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 B3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 B4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 B5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 B6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 B7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 B8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 B9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 BA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 BB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 BC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 BD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 BE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 BF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 C0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 C1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 C2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 C3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 C4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 C5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 C6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 C7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 C8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 C9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 CA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 CB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 CC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 CD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 CE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 CF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 D0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 D1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 D2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 D3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 D4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 D5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 D6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 D7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 D8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 D9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 DA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 DB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 DC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 DD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 DE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 DF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 E0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 E1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 E2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 E3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 E4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 E5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 E6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 E7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 E8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 E9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 EA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 EB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 EC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 ED 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 EE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 EF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 F0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 F1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 F2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 F3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 F4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 F5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 F6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 F7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 F8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 F9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 FA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 FB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 FC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 FD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 FE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 30 FF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 00 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 01 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 02 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 03 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 04 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 05 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 06 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 07 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 08 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 09 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 0A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 0B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 0C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 0D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 0E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 0F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 10 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 11 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 12 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 13 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 14 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 15 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 16 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 17 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 18 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 19 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 1A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 1B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 1C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 1D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 1E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 1F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 20 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 21 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 22 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 23 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 24 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 25 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 26 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 27 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 28 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 29 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 2A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 2B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 2C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 2D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 2E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 2F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 30 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 31 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 32 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 33 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 34 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 35 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 36 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 37 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 38 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 39 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 3A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 3B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 3C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 3D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 3E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 3F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 40 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 41 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 42 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 43 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 44 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 45 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 46 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 47 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 48 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 49 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 4A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 4B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 4C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 4D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 4E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 4F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 50 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 51 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 52 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 53 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 54 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 55 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 56 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 57 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 58 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 59 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 5A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 5B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 5C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 5D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 5E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 5F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 60 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 61 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 62 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 63 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 64 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 65 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 66 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 67 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 68 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 69 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 6A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 6B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 6C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 6D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 6E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 6F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 70 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 71 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 72 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 73 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 74 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 75 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 76 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 77 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 78 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 79 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 7A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 7B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 7C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 7D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 7E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 7F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 80 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 81 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 82 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 83 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 84 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 85 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 86 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 87 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 88 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 89 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 8A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 8B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 8C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 8D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 8E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 8F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 90 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 91 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 92 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 93 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 94 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 95 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 96 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 97 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 98 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 99 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 9A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 9B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 9C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 9D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 9E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 9F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 A0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 A1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 A2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 A3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 A4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 A5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 A6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 A7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 A8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 A9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 AA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 AB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 AC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 AD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 AE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 AF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 B0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 B1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 B2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 B3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 B4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 B5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 B6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 B7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 B8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 B9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 BA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 BB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 BC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 BD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 BE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 BF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 C0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 C1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 C2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 C3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 C4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 C5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 C6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 C7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 C8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 C9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 CA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 CB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 CC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 CD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 CE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 CF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 D0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 D1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 D2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 D3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 D4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 D5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 D6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 D7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 D8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 D9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 DA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 DB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 DC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 DD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 DE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 DF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 E0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 E1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 E2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 E3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 E4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 E5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 E6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 E7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 E8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 E9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 EA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 EB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 EC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 ED 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 EE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 EF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 F0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 F1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 F2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 F3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 F4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 F5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 F6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 F7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 F8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 F9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 FA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 FB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 FC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 FD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 FE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 31 FF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 00 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 01 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 02 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 03 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 04 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 05 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 06 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 07 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 08 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 09 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 0A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 0B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 0C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 0D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 0E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 0F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 10 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 11 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 12 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 13 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 14 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 15 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 16 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 17 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 18 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 19 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 1A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 1B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 1C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 1D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 1E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 1F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 20 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 21 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 22 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 23 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 24 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 25 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 26 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 27 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 28 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 29 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 2A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 2B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 2C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 2D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 2E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 2F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 30 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 31 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 32 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 33 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 34 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 35 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 36 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 37 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 38 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 39 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 3A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 3B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 3C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 3D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 3E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 3F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 40 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 41 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 42 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 43 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 44 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 45 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 46 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 47 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 48 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 49 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 4A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 4B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 4C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 4D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 4E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 4F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 50 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 51 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 52 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 53 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 54 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 55 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 56 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 57 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 58 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 59 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 5A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 5B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 5C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 5D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 5E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 5F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 60 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 61 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 62 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 63 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 64 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 65 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 66 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 67 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 68 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 69 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 6A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 6B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 6C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 6D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 6E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 6F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 70 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 71 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 72 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 73 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 74 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 75 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 76 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 77 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 78 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 79 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 7A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 7B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 7C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 7D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 7E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 7F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 80 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 81 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 82 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 83 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 84 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 85 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 86 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 87 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 88 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 89 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 8A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 8B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 8C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 8D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 8E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 8F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 90 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 91 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 92 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 93 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 94 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 95 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 96 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 97 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 98 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 99 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 9A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 9B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 9C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 9D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 9E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 9F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 A0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 A1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 A2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 A3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 A4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 A5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 A6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 A7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 A8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 A9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 AA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 AB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 AC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 AD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 AE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 AF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 B0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 B1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 B2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 B3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 B4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 B5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 B6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 B7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 B8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 B9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 BA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 BB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 BC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 BD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 BE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 BF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 C0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 C1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 C2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 C3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 C4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 C5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 C6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 C7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 C8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 C9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 CA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 CB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 CC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 CD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 CE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 CF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 D0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 D1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 D2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 D3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 D4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 D5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 D6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 D7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 D8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 D9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 DA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 DB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 DC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 DD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 DE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 DF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 E0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 E1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 E2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 E3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 E4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 E5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 E6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 E7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 E8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 E9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 EA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 EB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 EC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 ED 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 EE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 EF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 F0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 F1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 F2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 F3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 F4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 F5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 F6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 F7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 F8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 F9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 FA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 FB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 FC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 FD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 FE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 32 FF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 00 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 01 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 02 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 03 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 04 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 05 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 06 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 07 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 08 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 09 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 0A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 0B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 0C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 0D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 0E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 0F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 10 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 11 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 12 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 13 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 14 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 15 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 16 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 17 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 18 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 19 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 1A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 1B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 1C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 1D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 1E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 1F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 20 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 21 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 22 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 23 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 24 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 25 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 26 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 27 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 28 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 29 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 2A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 2B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 2C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 2D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 2E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 2F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 30 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 31 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 32 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 33 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 34 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 35 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 36 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 37 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 38 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 39 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 3A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 3B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 3C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 3D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 3E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 3F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 40 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 41 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 42 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 43 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 44 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 45 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 46 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 47 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 48 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 49 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 4A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 4B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 4C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 4D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 4E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 4F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 50 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 51 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 52 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 53 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 54 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 55 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 56 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 57 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 58 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 59 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 5A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 5B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 5C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 5D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 5E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 5F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 60 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 61 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 62 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 63 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 64 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 65 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 66 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 67 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 68 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 69 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 6A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 6B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 6C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 6D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 6E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 6F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 70 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 71 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 72 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 73 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 74 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 75 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 76 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 77 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 78 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 79 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 7A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 7B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 7C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 7D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 7E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 7F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 80 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 81 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 82 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 83 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 84 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 85 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 86 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 87 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 88 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 89 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 8A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 8B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 8C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 8D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 8E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 8F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 90 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 91 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 92 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 93 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 94 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 95 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 96 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 97 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 98 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 99 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 9A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 9B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 9C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 9D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 9E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 9F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 A0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 A1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 A2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 A3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 A4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 A5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 A6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 A7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 A8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 A9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 AA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 AB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 AC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 AD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 AE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 AF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 B0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 B1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 B2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 B3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 B4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 B5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 B6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 B7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 B8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 B9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 BA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 BB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 BC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 BD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 BE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 BF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 C0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 C1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 C2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 C3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 C4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 C5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 C6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 C7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 C8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 C9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 CA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 CB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 CC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 CD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 CE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 CF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 D0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 D1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 D2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 D3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 D4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 D5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 D6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 D7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 D8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 D9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 DA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 DB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 DC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 DD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 DE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 DF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 E0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 E1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 E2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 E3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 E4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 E5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 E6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 E7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 E8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 E9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 EA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 EB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 EC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 ED 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 EE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 EF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 F0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 F1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 F2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 F3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 F4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 F5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 F6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 F7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 F8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 F9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 FA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 FB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 FC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 FD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 FE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 33 FF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 00 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 01 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 02 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 03 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 04 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 05 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 06 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 07 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 08 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 09 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 0A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 0B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 0C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 0D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 0E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 0F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 10 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 11 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 12 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 13 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 14 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 15 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 16 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 17 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 18 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 19 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 1A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 1B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 1C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 1D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 1E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 1F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 20 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 21 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 22 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 23 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 24 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 25 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 26 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 27 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 28 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 29 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 2A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 2B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 2C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 2D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 2E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 2F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 30 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 31 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 32 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 33 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 34 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 35 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 36 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 37 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 38 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 39 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 3A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 3B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 3C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 3D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 3E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 3F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 40 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 41 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 42 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 43 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 44 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 45 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 46 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 47 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 48 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 49 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 4A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 4B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 4C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 4D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 4E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 4F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 50 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 51 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 52 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 53 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 54 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 55 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 56 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 57 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 58 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 59 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 5A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 5B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 5C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 5D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 5E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 5F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 60 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 61 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 62 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 63 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 64 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 65 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 66 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 67 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 68 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 69 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 6A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 6B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 6C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 6D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 6E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 6F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 70 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 71 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 72 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 73 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 74 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 75 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 76 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 77 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 78 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 79 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 7A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 7B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 7C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 7D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 7E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 7F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 80 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 81 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 82 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 83 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 84 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 85 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 86 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 87 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 88 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 89 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 8A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 8B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 8C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 8D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 8E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 8F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 90 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 91 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 92 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 93 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 94 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 95 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 96 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 97 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 98 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 99 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 9A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 9B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 9C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 9D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 9E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 9F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 A0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 A1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 A2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 A3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 A4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 A5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 A6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 A7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 A8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 A9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 AA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 AB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 AC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 AD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 AE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 AF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 B0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 B1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 B2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 B3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 B4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 B5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 B6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 B7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 B8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 B9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 BA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 BB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 BC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 BD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 BE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 BF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 C0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 C1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 C2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 C3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 C4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 C5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 C6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 C7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 C8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 C9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 CA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 CB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 CC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 CD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 CE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 CF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 D0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 D1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 D2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 D3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 D4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 D5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 D6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 D7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 D8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 D9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 DA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 DB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 DC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 DD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 DE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 DF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 E0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 E1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 E2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 E3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 E4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 E5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 E6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 E7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 E8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 E9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 EA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 EB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 EC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 ED 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 EE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 EF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 F0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 F1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 F2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 F3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 F4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 F5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 F6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 F7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 F8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 F9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 FA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 FB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 FC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 FD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 FE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 34 FF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 00 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 01 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 02 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 03 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 04 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 05 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 06 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 07 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 08 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 09 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 0A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 0B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 0C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 0D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 0E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 0F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 10 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 11 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 12 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 13 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 14 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 15 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 16 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 17 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 18 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 19 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 1A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 1B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 1C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 1D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 1E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 1F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 20 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 21 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 22 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 23 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 24 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 25 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 26 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 27 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 28 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 29 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 2A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 2B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 2C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 2D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 2E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 2F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 30 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 31 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 32 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 33 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 34 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 35 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 36 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 37 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 38 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 39 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 3A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 3B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 3C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 3D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 3E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 3F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 40 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 41 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 42 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 43 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 44 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 45 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 46 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 47 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 48 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 49 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 4A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 4B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 4C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 4D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 4E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 4F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 50 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 51 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 52 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 53 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 54 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 55 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 56 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 57 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 58 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 59 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 5A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 5B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 5C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 5D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 5E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 5F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 60 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 61 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 62 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 63 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 64 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 65 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 66 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 67 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 68 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 69 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 6A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 6B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 6C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 6D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 6E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 6F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 70 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 71 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 72 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 73 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 74 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 75 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 76 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 77 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 78 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 79 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 7A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 7B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 7C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 7D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 7E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 7F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 80 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 81 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 82 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 83 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 84 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 85 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 86 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 87 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 88 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 89 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 8A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 8B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 8C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 8D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 8E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 8F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 90 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 91 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 92 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 93 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 94 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 95 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 96 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 97 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 98 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 99 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 9A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 9B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 9C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 9D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 9E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 9F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 A0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 A1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 A2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 A3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 A4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 A5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 A6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 A7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 A8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 A9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 AA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 AB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 AC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 AD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 AE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 AF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 B0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 B1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 B2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 B3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 B4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 B5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 B6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 B7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 B8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 B9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 BA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 BB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 BC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 BD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 BE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 BF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 C0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 C1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 C2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 C3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 C4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 C5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 C6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 C7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 C8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 C9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 CA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 CB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 CC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 CD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 CE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 CF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 D0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 D1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 D2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 D3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 D4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 D5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 D6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 D7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 D8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 D9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 DA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 DB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 DC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 DD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 DE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 DF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 E0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 E1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 E2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 E3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 E4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 E5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 E6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 E7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 E8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 E9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 EA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 EB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 EC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 ED 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 EE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 EF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 F0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 F1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 F2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 F3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 F4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 F5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 F6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 F7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 F8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 F9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 FA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 FB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 FC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 FD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 FE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 35 FF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 00 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 01 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 02 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 03 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 04 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 05 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 06 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 07 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 08 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 09 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 0A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 0B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 0C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 0D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 0E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 0F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 10 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 11 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 12 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 13 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 14 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 15 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 16 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 17 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 18 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 19 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 1A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 1B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 1C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 1D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 1E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 1F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 20 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 21 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 22 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 23 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 24 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 25 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 26 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 27 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 28 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 29 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 2A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 2B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 2C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 2D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 2E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 2F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 30 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 31 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 32 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 33 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 34 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 35 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 36 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 37 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 38 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 39 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 3A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 3B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 3C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 3D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 3E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 3F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 40 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 41 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 42 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 43 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 44 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 45 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 46 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 47 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 48 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 49 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 4A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 4B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 4C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 4D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 4E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 4F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 50 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 51 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 52 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 53 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 54 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 55 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 56 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 57 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 58 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 59 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 5A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 5B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 5C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 5D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 5E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 5F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 60 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 61 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 62 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 63 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 64 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 65 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 66 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 67 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 68 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 69 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 6A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 6B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 6C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 6D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 6E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 6F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 70 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 71 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 72 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 73 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 74 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 75 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 76 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 77 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 78 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 79 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 7A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 7B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 7C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 7D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 7E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 7F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 80 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 81 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 82 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 83 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 84 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 85 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 86 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 87 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 88 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 89 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 8A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 8B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 8C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 8D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 8E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 8F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 90 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 91 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 92 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 93 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 94 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 95 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 96 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 97 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 98 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 99 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 9A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 9B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 9C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 9D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 9E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 9F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 A0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 A1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 A2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 A3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 A4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 A5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 A6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 A7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 A8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 A9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 AA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 AB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 AC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 AD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 AE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 AF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 B0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 B1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 B2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 B3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 B4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 B5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 B6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 B7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 B8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 B9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 BA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 BB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 BC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 BD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 BE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 BF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 C0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 C1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 C2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 C3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 C4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 C5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 C6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 C7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 C8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 C9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 CA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 CB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 CC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 CD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 CE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 CF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 D0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 D1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 D2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 D3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 D4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 D5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 D6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 D7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 D8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 D9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 DA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 DB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 DC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 DD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 DE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 DF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 E0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 E1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 E2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 E3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 E4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 E5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 E6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 E7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 E8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 E9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 EA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 EB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 EC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 ED 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 EE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 EF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 F0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 F1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 F2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 F3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 F4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 F5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 F6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 F7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 F8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 F9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 FA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 FB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 FC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 FD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 FE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 36 FF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 00 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 01 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 02 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 03 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 04 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 05 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 06 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 07 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 08 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 09 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 0A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 0B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 0C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 0D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 0E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 0F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 10 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 11 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 12 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 13 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 14 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 15 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 16 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 17 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 18 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 19 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 1A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 1B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 1C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 1D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 1E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 1F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 20 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 21 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 22 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 23 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 24 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 25 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 26 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 27 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 28 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 29 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 2A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 2B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 2C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 2D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 2E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 2F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 30 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 31 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 32 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 33 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 34 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 35 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 36 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 37 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 38 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 39 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 3A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 3B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 3C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 3D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 3E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 3F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 40 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 41 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 42 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 43 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 44 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 45 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 46 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 47 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 48 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 49 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 4A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 4B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 4C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 4D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 4E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 4F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 50 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 51 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 52 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 53 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 54 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 55 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 56 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 57 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 58 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 59 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 5A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 5B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 5C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 5D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 5E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 5F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 60 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 61 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 62 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 63 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 64 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 65 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 66 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 67 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 68 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 69 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 6A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 6B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 6C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 6D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 6E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 6F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 70 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 71 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 72 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 73 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 74 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 75 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 76 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 77 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 78 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 79 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 7A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 7B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 7C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 7D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 7E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 7F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 80 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 81 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 82 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 83 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 84 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 85 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 86 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 87 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 88 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 89 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 8A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 8B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 8C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 8D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 8E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 8F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 90 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 91 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 92 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 93 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 94 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 95 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 96 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 97 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 98 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 99 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 9A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 9B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 9C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 9D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 9E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 9F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 A0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 A1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 A2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 A3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 A4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 A5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 A6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 A7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 A8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 A9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 AA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 AB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 AC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 AD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 AE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 AF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 B0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 B1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 B2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 B3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 B4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 B5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 B6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 B7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 B8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 B9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 BA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 BB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 BC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 BD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 BE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 BF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 C0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 C1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 C2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 C3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 C4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 C5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 C6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 C7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 C8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 C9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 CA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 CB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 CC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 CD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 CE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 CF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 D0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 D1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 D2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 D3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 D4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 D5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 D6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 D7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 D8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 D9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 DA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 DB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 DC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 DD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 DE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 DF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 E0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 E1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 E2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 E3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 E4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 E5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 E6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 E7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 E8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 E9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 EA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 EB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 EC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 ED 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 EE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 EF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 F0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 F1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 F2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 F3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 F4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 F5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 F6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 F7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 F8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 F9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 FA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 FB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 FC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 FD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 FE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 37 FF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 00 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 01 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 02 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 03 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 04 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 05 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 06 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 07 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 08 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 09 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 0A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 0B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 0C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 0D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 0E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 0F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 10 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 11 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 12 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 13 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 14 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 15 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 16 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 17 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 18 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 19 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 1A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 1B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 1C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 1D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 1E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 1F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 20 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 21 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 22 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 23 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 24 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 25 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 26 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 27 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 28 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 29 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 2A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 2B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 2C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 2D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 2E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 2F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 30 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 31 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 32 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 33 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 34 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 35 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 36 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 37 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 38 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 39 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 3A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 3B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 3C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 3D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 3E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 3F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 40 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 41 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 42 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 43 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 44 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 45 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 46 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 47 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 48 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 49 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 4A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 4B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 4C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 4D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 4E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 4F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 50 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 51 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 52 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 53 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 54 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 55 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 56 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 57 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 58 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 59 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 5A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 5B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 5C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 5D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 5E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 5F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 60 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 61 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 62 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 63 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 64 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 65 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 66 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 67 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 68 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 69 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 6A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 6B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 6C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 6D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 6E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 6F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 70 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 71 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 72 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 73 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 74 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 75 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 76 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 77 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 78 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 79 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 7A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 7B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 7C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 7D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 7E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 7F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 80 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 81 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 82 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 83 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 84 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 85 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 86 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 87 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 88 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 89 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 8A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 8B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 8C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 8D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 8E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 8F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 90 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 91 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 92 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 93 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 94 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 95 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 96 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 97 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 98 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 99 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 9A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 9B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 9C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 9D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 9E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 9F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 A0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 A1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 A2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 A3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 A4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 A5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 A6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 A7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 A8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 A9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 AA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 AB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 AC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 AD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 AE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 AF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 B0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 B1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 B2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 B3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 B4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 B5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 B6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 B7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 B8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 B9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 BA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 BB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 BC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 BD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 BE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 BF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 C0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 C1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 C2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 C3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 C4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 C5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 C6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 C7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 C8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 C9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 CA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 CB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 CC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 CD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 CE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 CF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 D0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 D1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 D2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 D3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 D4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 D5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 D6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 D7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 D8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 D9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 DA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 DB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 DC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 DD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 DE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 DF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 E0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 E1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 E2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 E3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 E4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 E5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 E6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 E7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 E8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 E9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 EA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 EB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 EC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 ED 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 EE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 EF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 F0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 F1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 F2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 F3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 F4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 F5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 F6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 F7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 F8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 F9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 FA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 FB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 FC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 FD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 FE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 38 FF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 00 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 01 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 02 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 03 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 04 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 05 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 06 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 07 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 08 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 09 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 0A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 0B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 0C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 0D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 0E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 0F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 10 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 11 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 12 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 13 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 14 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 15 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 16 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 17 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 18 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 19 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 1A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 1B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 1C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 1D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 1E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 1F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 20 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 21 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 22 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 23 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 24 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 25 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 26 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 27 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 28 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 29 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 2A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 2B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 2C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 2D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 2E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 2F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 30 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 31 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 32 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 33 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 34 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 35 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 36 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 37 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 38 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 39 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 3A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 3B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 3C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 3D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 3E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 3F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 40 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 41 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 42 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 43 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 44 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 45 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 46 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 47 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 48 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 49 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 4A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 4B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 4C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 4D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 4E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 4F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 50 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 51 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 52 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 53 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 54 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 55 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 56 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 57 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 58 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 59 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 5A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 5B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 5C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 5D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 5E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 5F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 60 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 61 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 62 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 63 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 64 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 65 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 66 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 67 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 68 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 69 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 6A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 6B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 6C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 6D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 6E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 6F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 70 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 71 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 72 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 73 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 74 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 75 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 76 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 77 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 78 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 79 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 7A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 7B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 7C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 7D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 7E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 7F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 80 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 81 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 82 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 83 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 84 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 85 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 86 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 87 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 88 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 89 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 8A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 8B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 8C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 8D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 8E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 8F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 90 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 91 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 92 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 93 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 94 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 95 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 96 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 97 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 98 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 99 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 9A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 9B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 9C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 9D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 9E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 9F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 A0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 A1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 A2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 A3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 A4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 A5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 A6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 A7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 A8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 A9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 AA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 AB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 AC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 AD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 AE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 AF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 B0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 B1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 B2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 B3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 B4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 B5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 B6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 B7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 B8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 B9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 BA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 BB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 BC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 BD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 BE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 BF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 C0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 C1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 C2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 C3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 C4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 C5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 C6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 C7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 C8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 C9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 CA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 CB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 CC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 CD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 CE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 CF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 D0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 D1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 D2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 D3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 D4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 D5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 D6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 D7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 D8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 D9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 DA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 DB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 DC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 DD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 DE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 DF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 E0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 E1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 E2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 E3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 E4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 E5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 E6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 E7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 E8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 E9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 EA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 EB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 EC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 ED 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 EE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 EF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 F0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 F1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 F2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 F3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 F4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 F5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 F6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 F7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 F8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 F9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 FA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 FB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 FC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 FD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 FE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 39 FF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 00 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 01 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 02 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 03 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 04 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 05 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 06 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 07 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 08 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 09 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 0A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 0B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 0C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 0D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 0E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 0F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 10 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 11 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 12 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 13 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 14 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 15 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 16 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 17 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 18 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 19 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 1A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 1B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 1C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 1D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 1E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 1F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 20 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 21 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 22 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 23 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 24 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 25 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 26 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 27 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 28 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 29 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 2A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 2B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 2C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 2D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 2E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 2F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 30 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 31 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 32 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 33 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 34 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 35 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 36 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 37 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 38 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 39 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 3A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 3B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 3C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 3D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 3E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 3F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 40 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 41 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 42 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 43 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 44 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 45 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 46 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 47 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 48 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 49 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 4A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 4B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 4C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 4D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 4E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 4F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 50 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 51 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 52 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 53 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 54 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 55 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 56 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 57 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 58 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 59 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 5A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 5B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 5C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 5D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 5E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 5F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 60 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 61 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 62 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 63 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 64 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 65 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 66 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 67 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 68 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 69 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 6A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 6B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 6C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 6D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 6E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 6F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 70 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 71 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 72 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 73 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 74 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 75 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 76 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 77 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 78 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 79 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 7A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 7B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 7C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 7D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 7E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 7F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 80 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 81 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 82 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 83 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 84 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 85 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 86 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 87 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 88 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 89 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 8A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 8B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 8C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 8D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 8E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 8F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 90 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 91 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 92 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 93 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 94 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 95 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 96 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 97 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 98 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 99 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 9A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 9B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 9C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 9D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 9E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A 9F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A A0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A A1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A A2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A A3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A A4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A A5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A A6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A A7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A A8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A A9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A AA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A AB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A AC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A AD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A AE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A AF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A B0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A B1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A B2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A B3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A B4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A B5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A B6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A B7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A B8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A B9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A BA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A BB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A BC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A BD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A BE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A BF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A C0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A C1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A C2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A C3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A C4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A C5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A C6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A C7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A C8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A C9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A CA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A CB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A CC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A CD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A CE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A CF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A D0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A D1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A D2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A D3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A D4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A D5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A D6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A D7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A D8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A D9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A DA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A DB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A DC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A DD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A DE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A DF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A E0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A E1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A E2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A E3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A E4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A E5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A E6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A E7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A E8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A E9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A EA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A EB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A EC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A ED 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A EE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A EF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A F0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A F1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A F2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A F3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A F4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A F5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A F6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A F7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A F8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A F9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A FA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A FB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A FC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A FD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A FE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3A FF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 00 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 01 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 02 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 03 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 04 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 05 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 06 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 07 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 08 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 09 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 0A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 0B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 0C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 0D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 0E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 0F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 10 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 11 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 12 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 13 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 14 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 15 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 16 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 17 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 18 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 19 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 1A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 1B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 1C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 1D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 1E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 1F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 20 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 21 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 22 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 23 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 24 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 25 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 26 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 27 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 28 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 29 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 2A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 2B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 2C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 2D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 2E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 2F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 30 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 31 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 32 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 33 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 34 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 35 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 36 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 37 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 38 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 39 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 3A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 3B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 3C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 3D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 3E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 3F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 40 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 41 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 42 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 43 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 44 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 45 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 46 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 47 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 48 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 49 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 4A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 4B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 4C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 4D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 4E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 4F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 50 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 51 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 52 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 53 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 54 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 55 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 56 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 57 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 58 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 59 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 5A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 5B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 5C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 5D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 5E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 5F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 60 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 61 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 62 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 63 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 64 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 65 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 66 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 67 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 68 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 69 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 6A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 6B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 6C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 6D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 6E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 6F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 70 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 71 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 72 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 73 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 74 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 75 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 76 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 77 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 78 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 79 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 7A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 7B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 7C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 7D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 7E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 7F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 80 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 81 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 82 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 83 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 84 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 85 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 86 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 87 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 88 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 89 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 8A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 8B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 8C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 8D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 8E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 8F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 90 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 91 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 92 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 93 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 94 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 95 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 96 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 97 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 98 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 99 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 9A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 9B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 9C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 9D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 9E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B 9F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B A0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B A1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B A2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B A3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B A4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B A5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B A6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B A7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B A8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B A9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B AA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B AB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B AC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B AD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B AE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B AF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B B0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B B1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B B2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B B3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B B4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B B5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B B6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B B7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B B8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B B9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B BA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B BB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B BC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B BD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B BE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B BF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B C0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B C1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B C2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B C3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B C4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B C5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B C6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B C7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B C8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B C9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B CA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B CB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B CC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B CD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B CE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B CF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B D0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B D1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B D2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B D3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B D4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B D5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B D6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B D7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B D8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B D9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B DA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B DB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B DC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B DD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B DE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B DF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B E0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B E1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B E2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B E3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B E4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B E5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B E6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B E7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B E8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B E9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B EA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B EB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B EC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B ED 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B EE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B EF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B F0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B F1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B F2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B F3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B F4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B F5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B F6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B F7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B F8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B F9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B FA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B FB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B FC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B FD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B FE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3B FF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 00 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 01 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 02 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 03 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 04 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 05 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 06 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 07 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 08 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 09 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 0A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 0B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 0C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 0D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 0E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 0F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 10 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 11 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 12 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 13 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 14 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 15 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 16 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 17 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 18 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 19 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 1A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 1B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 1C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 1D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 1E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 1F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 20 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 21 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 22 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 23 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 24 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 25 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 26 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 27 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 28 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 29 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 2A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 2B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 2C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 2D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 2E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 2F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 30 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 31 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 32 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 33 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 34 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 35 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 36 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 37 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 38 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 39 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 3A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 3B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 3C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 3D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 3E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 3F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 40 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 41 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 42 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 43 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 44 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 45 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 46 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 47 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 48 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 49 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 4A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 4B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 4C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 4D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 4E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 4F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 50 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 51 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 52 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 53 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 54 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 55 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 56 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 57 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 58 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 59 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 5A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 5B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 5C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 5D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 5E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 5F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 60 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 61 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 62 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 63 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 64 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 65 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 66 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 67 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 68 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 69 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 6A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 6B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 6C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 6D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 6E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 6F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 70 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 71 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 72 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 73 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 74 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 75 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 76 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 77 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 78 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 79 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 7A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 7B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 7C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 7D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 7E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 7F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 80 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 81 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 82 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 83 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 84 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 85 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 86 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 87 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 88 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 89 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 8A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 8B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 8C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 8D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 8E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 8F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 90 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 91 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 92 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 93 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 94 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 95 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 96 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 97 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 98 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 99 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 9A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 9B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 9C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 9D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 9E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C 9F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C A0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C A1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C A2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C A3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C A4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C A5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C A6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C A7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C A8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C A9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C AA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C AB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C AC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C AD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C AE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C AF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C B0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C B1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C B2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C B3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C B4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C B5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C B6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C B7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C B8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C B9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C BA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C BB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C BC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C BD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C BE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C BF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C C0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C C1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C C2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C C3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C C4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C C5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C C6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C C7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C C8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C C9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C CA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C CB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C CC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C CD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C CE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C CF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C D0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C D1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C D2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C D3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C D4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C D5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C D6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C D7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C D8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C D9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C DA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C DB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C DC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C DD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C DE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C DF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C E0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C E1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C E2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C E3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C E4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C E5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C E6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C E7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C E8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C E9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C EA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C EB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C EC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C ED 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C EE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C EF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C F0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C F1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C F2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C F3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C F4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C F5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C F6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C F7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C F8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C F9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C FA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C FB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C FC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C FD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C FE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3C FF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 00 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 01 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 02 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 03 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 04 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 05 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 06 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 07 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 08 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 09 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 0A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 0B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 0C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 0D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 0E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 0F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 10 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 11 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 12 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 13 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 14 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 15 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 16 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 17 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 18 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 19 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 1A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 1B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 1C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 1D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 1E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 1F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 20 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 21 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 22 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 23 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 24 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 25 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 26 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 27 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 28 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 29 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 2A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 2B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 2C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 2D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 2E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 2F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 30 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 31 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 32 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 33 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 34 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 35 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 36 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 37 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 38 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 39 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 3A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 3B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 3C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 3D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 3E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 3F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 40 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 41 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 42 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 43 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 44 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 45 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 46 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 47 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 48 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 49 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 4A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 4B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 4C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 4D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 4E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 4F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 50 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 51 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 52 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 53 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 54 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 55 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 56 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 57 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 58 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 59 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 5A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 5B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 5C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 5D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 5E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 5F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 60 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 61 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 62 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 63 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 64 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 65 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 66 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 67 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 68 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 69 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 6A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 6B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 6C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 6D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 6E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 6F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 70 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 71 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 72 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 73 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 74 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 75 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 76 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 77 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 78 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 79 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 7A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 7B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 7C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 7D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 7E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 7F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 80 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 81 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 82 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 83 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 84 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 85 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 86 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 87 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 88 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 89 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 8A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 8B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 8C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 8D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 8E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 8F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 90 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 91 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 92 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 93 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 94 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 95 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 96 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 97 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 98 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 99 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 9A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 9B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 9C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 9D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 9E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D 9F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D A0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D A1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D A2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D A3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D A4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D A5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D A6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D A7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D A8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D A9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D AA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D AB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D AC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D AD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D AE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D AF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D B0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D B1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D B2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D B3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D B4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D B5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D B6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D B7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D B8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D B9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D BA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D BB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D BC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D BD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D BE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D BF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D C0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D C1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D C2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D C3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D C4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D C5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D C6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D C7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D C8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D C9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D CA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D CB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D CC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D CD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D CE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D CF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D D0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D D1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D D2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D D3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D D4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D D5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D D6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D D7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D D8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D D9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D DA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D DB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D DC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D DD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D DE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D DF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D E0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D E1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D E2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D E3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D E4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D E5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D E6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D E7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D E8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D E9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D EA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D EB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D EC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D ED 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D EE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D EF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D F0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D F1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D F2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D F3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D F4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D F5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D F6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D F7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D F8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D F9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D FA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D FB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D FC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D FD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D FE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3D FF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 00 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 01 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 02 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 03 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 04 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 05 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 06 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 07 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 08 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 09 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 0A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 0B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 0C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 0D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 0E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 0F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 10 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 11 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 12 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 13 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 14 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 15 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 16 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 17 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 18 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 19 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 1A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 1B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 1C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 1D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 1E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 1F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 20 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 21 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 22 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 23 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 24 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 25 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 26 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 27 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 28 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 29 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 2A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 2B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 2C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 2D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 2E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 2F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 30 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 31 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 32 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 33 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 34 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 35 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 36 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 37 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 38 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 39 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 3A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 3B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 3C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 3D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 3E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 3F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 40 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 41 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 42 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 43 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 44 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 45 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 46 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 47 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 48 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 49 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 4A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 4B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 4C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 4D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 4E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 4F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 50 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 51 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 52 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 53 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 54 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 55 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 56 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 57 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 58 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 59 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 5A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 5B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 5C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 5D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 5E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 5F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 60 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 61 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 62 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 63 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 64 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 65 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 66 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 67 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 68 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 69 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 6A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 6B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 6C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 6D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 6E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 6F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 70 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 71 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 72 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 73 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 74 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 75 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 76 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 77 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 78 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 79 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 7A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 7B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 7C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 7D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 7E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 7F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 80 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 81 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 82 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 83 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 84 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 85 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 86 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 87 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 88 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 89 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 8A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 8B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 8C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 8D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 8E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 8F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 90 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 91 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 92 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 93 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 94 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 95 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 96 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 97 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 98 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 99 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 9A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 9B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 9C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 9D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 9E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E 9F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E A0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E A1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E A2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E A3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E A4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E A5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E A6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E A7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E A8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E A9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E AA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E AB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E AC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E AD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E AE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E AF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E B0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E B1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E B2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E B3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E B4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E B5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E B6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E B7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E B8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E B9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E BA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E BB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E BC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E BD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E BE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E BF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E C0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E C1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E C2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E C3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E C4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E C5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E C6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E C7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E C8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E C9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E CA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E CB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E CC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E CD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E CE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E CF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E D0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E D1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E D2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E D3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E D4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E D5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E D6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E D7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E D8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E D9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E DA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E DB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E DC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E DD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E DE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E DF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E E0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E E1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E E2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E E3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E E4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E E5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E E6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E E7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E E8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E E9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E EA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E EB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E EC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E ED 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E EE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E EF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E F0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E F1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E F2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E F3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E F4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E F5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E F6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E F7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E F8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E F9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E FA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E FB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E FC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E FD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E FE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3E FF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 00 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 01 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 02 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 03 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 04 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 05 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 06 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 07 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 08 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 09 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 0A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 0B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 0C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 0D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 0E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 0F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 10 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 11 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 12 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 13 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 14 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 15 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 16 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 17 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 18 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 19 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 1A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 1B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 1C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 1D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 1E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 1F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 20 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 21 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 22 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 23 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 24 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 25 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 26 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 27 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 28 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 29 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 2A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 2B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 2C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 2D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 2E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 2F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 30 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 31 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 32 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 33 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 34 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 35 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 36 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 37 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 38 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 39 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 3A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 3B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 3C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 3D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 3E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 3F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 40 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 41 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 42 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 43 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 44 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 45 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 46 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 47 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 48 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 49 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 4A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 4B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 4C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 4D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 4E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 4F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 50 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 51 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 52 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 53 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 54 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 55 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 56 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 57 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 58 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 59 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 5A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 5B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 5C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 5D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 5E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 5F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 60 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 61 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 62 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 63 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 64 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 65 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 66 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 67 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 68 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 69 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 6A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 6B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 6C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 6D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 6E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 6F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 70 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 71 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 72 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 73 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 74 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 75 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 76 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 77 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 78 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 79 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 7A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 7B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 7C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 7D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 7E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 7F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 80 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 81 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 82 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 83 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 84 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 85 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 86 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 87 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 88 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 89 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 8A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 8B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 8C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 8D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 8E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 8F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 90 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 91 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 92 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 93 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 94 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 95 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 96 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 97 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 98 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 99 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 9A 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 9B 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 9C 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 9D 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 9E 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F 9F 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F A0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F A1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F A2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F A3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F A4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F A5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F A6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F A7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F A8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F A9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F AA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F AB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F AC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F AD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F AE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F AF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F B0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F B1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F B2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F B3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F B4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F B5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F B6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F B7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F B8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F B9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F BA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F BB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F BC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F BD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F BE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F BF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F C0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F C1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F C2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F C3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F C4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F C5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F C6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F C7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F C8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F C9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F CA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F CB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F CC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F CD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F CE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F CF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F D0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F D1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F D2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F D3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F D4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F D5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F D6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F D7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F D8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F D9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F DA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F DB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F DC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F DD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F DE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F DF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F E0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F E1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F E2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F E3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F E4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F E5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F E6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F E7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F E8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F E9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F EA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F EB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F EC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F ED 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F EE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F EF 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F F0 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F F1 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F F2 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F F3 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F F4 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F F5 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F F6 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F F7 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F F8 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F F9 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F FA 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F FB 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F FC 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F FD 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F FE 00\n", "Recv:  7F 31 31\n", "Send:  31 01 3F FF 00\n", "Recv:  7F 31 31\n"]}], "source": ["isoTpMessage = ISOTP(binascii.a2b_hex('1003'))\n", "pkg = isoTpSocket.sr1(isoTpMessage,verbose=0,timeout=5)\n", "print('Send: ',insert_spaces('1003'))\n", "print('Recv: ',insert_spaces(binascii.b2a_hex(pkg.data).decode('utf-8')))\n", "\n", "for each in output_list:\n", "    isoTpMessage = ISOTP(raw(each))\n", "    pkg = isoTpSocket.sr1(isoTpMessage,verbose=0)\n", "    print('Send: ',insert_spaces(binascii.b2a_hex(raw(each)).decode('utf-8')))\n", "    print('Recv: ',insert_spaces(binascii.b2a_hex(pkg.data).decode('utf-8')))\n", "    if UDS(pkg.data).service == 0x7f and UDS(pkg.data).negativeResponseCode == 0x78:\n", "        pkg = isoTpSocket.recv_raw()\n", "        print('Recv: ',insert_spaces(binascii.b2a_hex(pkg[1]).decode('utf-8')))"]}, {"cell_type": "markdown", "id": "3558ce02-002d-4567-8c86-78f307a037ef", "metadata": {}, "source": ["## L2解锁"]}, {"cell_type": "code", "execution_count": 4, "id": "14e968c3-65b4-4274-8c76-4bd86c4f6468", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Send:  10 03\n", "Recv:  7F 27 35\n", "Send:  27 03\n", "Recv:  50 03 00 32 00 C8\n"]}], "source": ["# high, low = 0xb0, 0x9a # CCU\n", "# high, low = 0xa8, 0x3d # ACM\n", "# high, low = 0xcd, 0xe5 # BMS\n", "# high, low = 0x69, 0x74 # BCU\n", "# high, low = 0x6f, 0x2b # EPS\n", "# high, low = 0xe3, 0xa1 # EDS\n", "# high, low = 0x3e, 0x9b # EVCC\n", "# high, low = 0x2f, 0xea # LBM\n", "# high, low = 0x35, 0x29 # BCM\n", "high, low = 0x5a, 0x9f # RAD_FC\n", "# high, low = 0xc4, 0x71 # RAD_SR\n", "# high, low = 0x1b, 0xcf # IC\n", "# high, low = 0xb2, 0x6a # ICS\n", "# high, low = 0x6d, 0xa9 # OBCM\n", "\n", "sendmsg = ['1003','2703']\n", "for each in sendmsg:\n", "    isoTpMessage = ISOTP(binascii.a2b_hex(each))\n", "    pkg = isoTpSocket.sr1(isoTpMessage,verbose=0)\n", "    print('Send: ',insert_spaces(each))\n", "    print('Recv: ',insert_spaces(binascii.b2a_hex(pkg.data).decode('utf-8')))\n", "    udsdata = UDS(pkg.data)\n", "    if udsdata.service == 0x7f and udsdata.negativeResponseCode == 0x78:\n", "        pkg = isoTpSocket.recv_raw()\n", "        print('Recv: ',insert_spaces(binascii.b2a_hex(pkg[1]).decode('utf-8')))\n", "        udsdata = UDS(pkg[1])\n", "        \n", "    elif udsdata.service == 0x67 and udsdata.securityAccessType == 0x3:\n", "        seed = binascii.b2a_hex(UDS(pkg.data).securitySeed).decode('utf-8')\n", "        key = ASAP1A__CCP_ComputeKeyFromSeed(seed,high,low)\n", "        isoTpMessage = ISOTP(binascii.a2b_hex('2704'+key))\n", "        pkg = isoTpSocket.sr1(isoTpMessage,verbose=0)\n", "        print('Send: ',insert_spaces('2704'+key))\n", "        print('Recv: ',insert_spaces(binascii.b2a_hex(pkg.data).decode('utf-8')))\n", "    time.sleep(0.05)"]}, {"cell_type": "markdown", "id": "bd6f1c7b-636c-4e6c-826e-dea5669e362a", "metadata": {"tags": []}, "source": ["## L4解锁"]}, {"cell_type": "code", "execution_count": 3, "id": "8cd3a0aa-0cc5-4749-9c9b-5b7b19694eed", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Send:  10 03\n", "Recv:  50 03 00 32 00 C8\n", "Send:  10 02\n", "Recv:  7F 10 78\n", "Recv:  50 02 00 32 00 C8\n", "Send:  27 07\n", "Recv:  7F 27 78\n", "Recv:  67 07 00 6E EE CF\n", "Send:  27 08 FD D7 68 00\n", "Recv:  7F 27 78\n"]}], "source": ["# high, low = 0x7f,0x29 #BTA1\n", "# high, low = 0x2A,0x7B #CCU\n", "# high, low = 0x9b,0xc5 #ACM\n", "# high, low = 0x6e, 0xa3 # BMS\n", "# high, low = 0x93, 0x28 # BCU\n", "# high, low = 0xc9, 0x3a # EPS\n", "# high, low = 0xd6, 0x72 # EDS\n", "# high, low = 0xad, 0xe6 # EVCC\n", "# high, low = 0xb4, 0xec # LBM\n", "# high, low = 0x62, 0x94 # BCM\n", "high, low = 0x3d, 0x8e # RAD_FC\n", "# high, low = 0x3d, 0x6fa # RAD_SR\n", "# high, low = 0xde, 0x94 # IC\n", "# high, low = 0x69, 0xfa # ICS\n", "# high, low = 0xca, 0x79 # OBCM\n", "# high, low = 0xa0,0xce #BTA3\n", "# high, low = 0x3d,0xa7 #BTA5\n", "sendmsg = ['1003','1002','2707']\n", "for each in sendmsg:\n", "    isoTpMessage = ISOTP(binascii.a2b_hex(each))\n", "    pkg = isoTpSocket.sr1(isoTpMessage,verbose=0,timeout=5)\n", "    udsdata = UDS(pkg.data)\n", "    print('Send: ',insert_spaces(each))\n", "    print('Recv: ',insert_spaces(binascii.b2a_hex(pkg.data).decode('utf-8')))\n", "    \n", "    while udsdata.service == 0x7f and udsdata.negativeResponseCode == 0x78:\n", "        pkg = isoTpSocket.recv_raw()\n", "        print('Recv: ',insert_spaces(binascii.b2a_hex(pkg[1]).decode('utf-8')))\n", "        udsdata = UDS(pkg[1])\n", "        \n", "    if udsdata.service == 0x67 and udsdata.securityAccessType == 0x7:\n", "        seed = binascii.b2a_hex(udsdata.securitySeed).decode('utf-8')\n", "        key = ASAP1A__CCP_ComputeKeyFromSeed(seed,high,low)\n", "        isoTpMessage = ISOTP(binascii.a2b_hex('2708'+key))\n", "        pkg = isoTpSocket.sr1(isoTpMessage,verbose=0)\n", "        print('Send: ',insert_spaces('2708'+key))\n", "        print('Recv: ',insert_spaces(binascii.b2a_hex(pkg.data).decode('utf-8')))"]}, {"cell_type": "code", "execution_count": null, "id": "84a0fccf-fe12-4ce4-b9c5-9654890949a1", "metadata": {}, "outputs": [], "source": ["UDS()"]}, {"cell_type": "code", "execution_count": 6, "id": "2693aee4-b858-46dc-8bc5-992ea9e3bb6c", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["'0x77f'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["hex(0x60f ^ 0x68f ^ 0x7ff)"]}, {"cell_type": "markdown", "id": "7caf66af-04f7-4d17-825e-c84eedd19a12", "metadata": {}, "source": ["# VIN 数据保护 "]}, {"cell_type": "markdown", "id": "fe85676d-4c90-44ef-bf9c-54c61743db52", "metadata": {}, "source": ["### VIN码注入-未通过安全访问"]}, {"cell_type": "code", "execution_count": 4, "id": "e631a0a8-7625-4800-b051-1b10e66b18c3", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Send:  22 F1 18\n", "Recv:  62 F1 18 50 30 33 33 34 34 32 37 20 41 4C\n", "Send:  22 F1 10\n", "Recv:  62 F1 10 41 0B 0D 00 00 00 00 00 00 00 00\n"]}], "source": ["sendmsg = ['22f118','22f110']\n", "for each in sendmsg:\n", "    isoTpMessage = ISOTP(binascii.a2b_hex(each))\n", "    pkg = isoTpSocket.sr1(isoTpMessage,verbose=0)\n", "    udsdata = UDS(pkg.data)\n", "    print('Send: ',insert_spaces(each))\n", "    print('Recv: ',insert_spaces(binascii.b2a_hex(pkg.data).decode('utf-8')))\n", "    \n", "    while udsdata.service == 0x7f and udsdata.negativeResponseCode == 0x78:\n", "        pkg = isoTpSocket.recv_raw()\n", "        print('Recv: ',insert_spaces(binascii.b2a_hex(pkg[1]).decode('utf-8')))\n", "        udsdata = UDS(pkg[1])"]}, {"cell_type": "code", "execution_count": 3, "id": "279d9432-348b-4c5c-bad2-9d2d9036ef6f", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Send:  22 F1 90\n", "Recv:  62 F1 90 48 4A 4E 46 42 41 42 4E 58 50 50 30 30 30 31 31 39\n"]}], "source": ["sendmsg = ['22f190']\n", "for each in sendmsg:\n", "    isoTpMessage = ISOTP(binascii.a2b_hex(each))\n", "    pkg = isoTpSocket.sr1(isoTpMessage,verbose=0)\n", "    udsdata = UDS(pkg.data)\n", "    print('Send: ',insert_spaces(each))\n", "    print('Recv: ',insert_spaces(binascii.b2a_hex(pkg.data).decode('utf-8')))\n", "    \n", "    while udsdata.service == 0x7f and udsdata.negativeResponseCode == 0x78:\n", "        pkg = isoTpSocket.recv_raw()\n", "        print('Recv: ',insert_spaces(binascii.b2a_hex(pkg[1]).decode('utf-8')))\n", "        udsdata = UDS(pkg[1])"]}, {"cell_type": "code", "execution_count": 3, "id": "072b0bc7-29a5-4c8c-b0c8-88f57ccee262", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Send:  10 03\n", "Recv:  7F 10 7E\n", "Send:  22 F1 90\n", "Recv:  7F 22 31\n"]}], "source": ["sendmsg = ['22f190','2ef19050414e4755303137353138333338333336','1003','2ef19050414e4755303137353138333338333336','1002','2ef19050414e4755303137353138333338333336']\n", "sendmsg = ['1003','22f190']\n", "for each in sendmsg:\n", "    isoTpMessage = ISOTP(binascii.a2b_hex(each))\n", "    pkg = isoTpSocket.sr1(isoTpMessage,verbose=0)\n", "    udsdata = UDS(pkg.data)\n", "    print('Send: ',insert_spaces(each))\n", "    print('Recv: ',insert_spaces(binascii.b2a_hex(pkg.data).decode('utf-8')))\n", "    \n", "    while udsdata.service == 0x7f and udsdata.negativeResponseCode == 0x78:\n", "        pkg = isoTpSocket.recv_raw()\n", "        print('Recv: ',insert_spaces(binascii.b2a_hex(pkg[1]).decode('utf-8')))\n", "        udsdata = UDS(pkg[1])"]}, {"cell_type": "markdown", "id": "14a1d6e5-67e7-4ad4-bc64-810f19e1fbfa", "metadata": {"tags": []}, "source": ["### VIN码注入错误长度-L2安全访问"]}, {"cell_type": "code", "execution_count": 3, "id": "eb545aa2-b9aa-4fd9-a116-63f32bf49062", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Send:  22 F1 90\n", "Recv:  62 F1 90 48 4A 4E 46 42 41 42 4E 58 52 50 30 30 30 32 38 34\n", "Send:  10 03\n", "Recv:  50 03 00 32 00 C8\n", "Send:  27 03\n", "Recv:  67 03 87 93 FF AB\n", "Send:  27 04 13 E6 63 0D\n", "Recv:  67 04\n", "Send:  2E F1 90 50 41 4E 47 55 30 31 37 35 31 38 33 33 38 33 33\n", "Recv:  7F 2E 13\n", "Send:  22 F1 90\n", "Recv:  62 F1 90 48 4A 4E 46 42 41 42 4E 58 52 50 30 30 30 32 38 34\n", "Send:  2E F1 90 2E <PERSON> 90 50 41 4E 47 55 30 31 37 35 31 38 33 33 38 33 33 36 36\n", "Recv:  7F 2E 13\n", "Send:  22 F1 90\n", "Recv:  62 F1 90 48 4A 4E 46 42 41 42 4E 58 52 50 30 30 30 32 38 34\n"]}], "source": ["# high, low = 0xa8, 0x3d # ACM\n", "# high, low = 0x6f, 0x2b # EPS\n", "high, low = 0xe3, 0xa1 # EDS\n", "\n", "\n", "sendmsg = ['22f190','1003','2703','2ef19050414e47553031373531383333383333','22f190','2ef1902ef19050414e475530313735313833333833333636','22f190']\n", "for each in sendmsg:\n", "    isoTpMessage = ISOTP(binascii.a2b_hex(each))\n", "    pkg = isoTpSocket.sr1(isoTpMessage,verbose=0)\n", "    print('Send: ',insert_spaces(each))\n", "    print('Recv: ',insert_spaces(binascii.b2a_hex(pkg.data).decode('utf-8')))\n", "    udsdata = UDS(pkg.data)\n", "    if udsdata.service == 0x7f and udsdata.negativeResponseCode == 0x78:\n", "        pkg = isoTpSocket.recv_raw()\n", "        print('Recv: ',insert_spaces(binascii.b2a_hex(pkg[1]).decode('utf-8')))\n", "        udsdata = UDS(pkg[1])\n", "        \n", "    elif udsdata.service == 0x67 and udsdata.securityAccessType == 0x3:\n", "        seed = binascii.b2a_hex(UDS(pkg.data).securitySeed).decode('utf-8')\n", "        key = ASAP1A__CCP_ComputeKeyFromSeed(seed,high,low)\n", "        isoTpMessage = ISOTP(binascii.a2b_hex('2704'+key))\n", "        pkg = isoTpSocket.sr1(isoTpMessage,verbose=0)\n", "        print('Send: ',insert_spaces('2704'+key))\n", "        print('Recv: ',insert_spaces(binascii.b2a_hex(pkg.data).decode('utf-8')))\n", "    time.sleep(0.05)"]}, {"cell_type": "markdown", "id": "2f4c1306-8a79-4e55-9778-c96fe0ff4dc9", "metadata": {"tags": []}, "source": ["### VIN码注入-L4安全访问"]}, {"cell_type": "code", "execution_count": 3, "id": "38333a34-5124-40b1-9c29-12cb4a34581a", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Send:  22 F1 90\n", "Recv:  62 F1 90 48 4A 4E 46 42 41 42 4E 58 50 50 30 30 30 31 31 39\n", "Send:  10 03\n", "Recv:  50 03 00 32 00 C8\n", "Send:  10 02\n", "Recv:  7F 10 78\n", "Recv:  50 02 00 32 00 C8\n", "Send:  27 07\n", "Recv:  67 07 9B 4F 52 F4\n", "Send:  27 08 93 E2 D4 D7\n", "Recv:  7F 27 78\n", "Send:  2E F1 90 50 41 4E 47 55 30 31 37 35 31 38 33 33 38 33 33 36\n", "Recv:  67 08\n", "Send:  10 01\n", "Recv:  7F 2E 31\n", "Send:  22 F1 90\n", "Recv:  7F 22 31\n"]}], "source": ["# high, low = 0x9b,0xc5 #ACM\n", "# high, low = 0xc9, 0x3a # EPS\n", "high, low = 0xd6, 0x72 # EDS\n", "\n", "sendmsg = ['22f190','1003','1002','2707','2ef19050414e4755303137353138333338333336','1001','22f190']\n", "for each in sendmsg:\n", "    isoTpMessage = ISOTP(binascii.a2b_hex(each))\n", "    pkg = isoTpSocket.sr1(isoTpMessage,verbose=0,timeout=5)\n", "    udsdata = UDS(pkg.data)\n", "    print('Send: ',insert_spaces(each))\n", "    print('Recv: ',insert_spaces(binascii.b2a_hex(pkg.data).decode('utf-8')))\n", "    \n", "    while udsdata.service == 0x7f and udsdata.negativeResponseCode == 0x78:\n", "        pkg = isoTpSocket.recv_raw()\n", "        print('Recv: ',insert_spaces(binascii.b2a_hex(pkg[1]).decode('utf-8')))\n", "        udsdata = UDS(pkg[1])\n", "        \n", "    if udsdata.service == 0x67 and udsdata.securityAccessType == 0x7:\n", "        seed = binascii.b2a_hex(udsdata.securitySeed).decode('utf-8')\n", "        key = ASAP1A__CCP_ComputeKeyFromSeed(seed,high,low)\n", "        isoTpMessage = ISOTP(binascii.a2b_hex('2708'+key))\n", "        pkg = isoTpSocket.sr1(isoTpMessage,verbose=0)\n", "        print('Send: ',insert_spaces('2708'+key))\n", "        print('Recv: ',insert_spaces(binascii.b2a_hex(pkg.data).decode('utf-8')))"]}, {"cell_type": "markdown", "id": "511305e9-066c-49ab-9d6f-17dba6a20786", "metadata": {}, "source": ["### VIN码注入-L2安全访问"]}, {"cell_type": "code", "execution_count": 3, "id": "44cd49db-ad20-4c2c-bdac-649d4b48ce50", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Send:  22 F1 90\n", "Recv:  7F 22 31\n", "Send:  10 03\n", "Recv:  7F 10 7E\n", "Send:  27 03\n", "Recv:  7F 27 12\n", "Send:  2E F1 90 48 4A 4E 46 42 41 42 4E 58 52 50 30 30 30 32 38 34\n", "Recv:  7F 2E 31\n", "Send:  22 F1 90\n", "Recv:  7F 22 31\n"]}], "source": ["# high, low = 0xa8, 0x3d # ACM\n", "# high, low = 0x6f, 0x2b # EPS\n", "high, low = 0xe3, 0xa1 # EDS\n", "\n", "\n", "sendmsg = ['22f190','1003','2703','2ef190484a4e464241424e585250303030323834','22f190']\n", "for each in sendmsg:\n", "    isoTpMessage = ISOTP(binascii.a2b_hex(each))\n", "    pkg = isoTpSocket.sr1(isoTpMessage,verbose=0)\n", "    print('Send: ',insert_spaces(each))\n", "    print('Recv: ',insert_spaces(binascii.b2a_hex(pkg.data).decode('utf-8')))\n", "    udsdata = UDS(pkg.data)\n", "    if udsdata.service == 0x7f and udsdata.negativeResponseCode == 0x78:\n", "        pkg = isoTpSocket.recv_raw()\n", "        print('Recv: ',insert_spaces(binascii.b2a_hex(pkg[1]).decode('utf-8')))\n", "        udsdata = UDS(pkg[1])\n", "        \n", "    elif udsdata.service == 0x67 and udsdata.securityAccessType == 0x3:\n", "        seed = binascii.b2a_hex(UDS(pkg.data).securitySeed).decode('utf-8')\n", "        key = ASAP1A__CCP_ComputeKeyFromSeed(seed,high,low)\n", "        isoTpMessage = ISOTP(binascii.a2b_hex('2704'+key))\n", "        pkg = isoTpSocket.sr1(isoTpMessage,verbose=0)\n", "        print('Send: ',insert_spaces('2704'+key))\n", "        print('Recv: ',insert_spaces(binascii.b2a_hex(pkg.data).decode('utf-8')))\n", "    time.sleep(0.05)"]}, {"cell_type": "code", "execution_count": 3, "id": "6cdde880-fdff-4a42-ad20-38e519606ada", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["'0x77f'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["hex(0x60F^0x68F^0x7ff)"]}, {"cell_type": "code", "execution_count": null, "id": "5b4019ac-33ba-4b14-8971-3143f2abc3b5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 5}