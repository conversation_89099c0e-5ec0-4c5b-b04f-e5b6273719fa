{"cells": [{"cell_type": "code", "execution_count": 5, "id": "1e364b82-c32d-4ba2-8c69-61ad30b72f78", "metadata": {"tags": []}, "outputs": [], "source": ["import cmd2\n", "import sys\n", "import os\n", "from utils.scanUDSid import *\n", "import getopt\n", "import logging\n", "import binascii\n", "from scapy.contrib.cansocket import CANSocket\n", "from scapy.config import conf\n", "import json\n", "import time\n", "from scapy.layers.can import CAN\n", "from scapy.contrib.isotp import ISOTPSocket\n", "from scapy.contrib.automotive.uds_scan import * \n", "from scapy.layers.can import *\n", "from scapy.main import load_contrib\n", "from reprint import output\n", "conf.contribs['ISOTP'] = {'use-can-isotp-kernel-module': False}\n", "conf.contribs['CANSocket'] = {'use-python-can': True}\n", "load_contrib('isotp')\n", "load_contrib('automotive.uds')"]}, {"cell_type": "code", "execution_count": 6, "id": "3a4e8107", "metadata": {}, "outputs": [], "source": ["def Bitmask(cid, sid):\n", "    if cid > 0x7ff or sid > 0x7ff:\n", "        return cid ^ sid ^ 0x1fffffff\n", "    else:\n", "        return cid ^ sid ^ 0x7ff"]}, {"cell_type": "code", "execution_count": 7, "id": "f53227ec", "metadata": {}, "outputs": [], "source": ["tx = 0x18E06188\n", "rx = 0x18E06180\n", "isoTpSocket = ISOTPSocket(CANSocket(bustype='socketcan', channel='can0',fd=True,\n", "                                                    can_filters=[{'can_id': tx, 'can_mask': Bitmask(tx,rx)},\n", "                                                    {'can_id': rx, 'can_mask': Bitmask(rx,tx)}]),\n", "                                                    tx_id=tx, rx_id=rx,padding=True,basecls=ISOTP)\n"]}, {"cell_type": "code", "execution_count": 8, "id": "8f36e54a", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on class NativeCANSocket in module scapy.contrib.cansocket_native:\n", "\n", "class NativeCANSocket(scapy.supersocket.SuperSocket)\n", " |  NativeCANSocket(channel=None, receive_own_messages=False, can_filters=None, fd=False, basecls=<class 'scapy.layers.can.CAN'>, **kwargs)\n", " |  \n", " |  Initializes a Linux PF_CAN socket object.\n", " |  \n", " |  Example:\n", " |      >>> socket = NativeCANSocket(channel=\"vcan0\", can_filters=[{'can_id': 0x200, 'can_mask': 0x7FF}])\n", " |  \n", " |  :param channel: Network interface name\n", " |  :param receive_own_messages: Messages, sent by this socket are will\n", " |                               also be received.\n", " |  :param can_filters: A list of can filter dictionaries.\n", " |  :param basecls: Packet type in which received data gets interpreted.\n", " |  :param kwargs: Various keyword arguments for compatibility with\n", " |                 PythonCANSockets\n", " |  \n", " |  Method resolution order:\n", " |      NativeCANSocket\n", " |      scapy.supersocket.SuperSocket\n", " |      builtins.object\n", " |  \n", " |  Methods defined here:\n", " |  \n", " |  __init__(self, channel=None, receive_own_messages=False, can_filters=None, fd=False, basecls=<class 'scapy.layers.can.CAN'>, **kwargs)\n", " |      Initialize self.  See help(type(self)) for accurate signature.\n", " |  \n", " |  recv_raw(self, x=16)\n", " |      Returns a tuple containing (cls, pkt_data, time)\n", " |  \n", " |  send(self, x)\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Data and other attributes defined here:\n", " |  \n", " |  desc = 'read/write packets at a given CAN interface using PF_CAN socke...\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Methods inherited from scapy.supersocket.SuperSocket:\n", " |  \n", " |  __del__(self)\n", " |      Close the socket\n", " |  \n", " |  __enter__(self)\n", " |  \n", " |  __exit__(self, exc_type, exc_value, traceback)\n", " |      Close the socket\n", " |  \n", " |  am(self, cls, *args, **kwargs)\n", " |      Creates an AnsweringMachine associated with this socket.\n", " |      \n", " |      :param cls: A subclass of AnsweringMachine to instantiate\n", " |  \n", " |  close(self)\n", " |  \n", " |  fileno(self)\n", " |  \n", " |  recv(self, x=65535)\n", " |  \n", " |  sniff(self, *args, **kargs)\n", " |  \n", " |  sr(self, *args, **kargs)\n", " |  \n", " |  sr1(self, *args, **kargs)\n", " |  \n", " |  tshark(self, *args, **kargs)\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Static methods inherited from scapy.supersocket.SuperSocket:\n", " |  \n", " |  select(sockets, remain=0.05)\n", " |      This function is called during sendrecv() routine to select\n", " |      the available sockets.\n", " |      \n", " |      :param sockets: an array of sockets that need to be selected\n", " |      :returns: an array of sockets that were selected and\n", " |          the function to be called next to get the packets (i.g. recv)\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Data descriptors inherited from scapy.supersocket.SuperSocket:\n", " |  \n", " |  __dict__\n", " |      dictionary for instance variables (if defined)\n", " |  \n", " |  __weakref__\n", " |      list of weak references to the object (if defined)\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Data and other attributes inherited from scapy.supersocket.SuperSocket:\n", " |  \n", " |  auxdata_available = False\n", " |  \n", " |  closed = False\n", " |  \n", " |  nonblocking_socket = False\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING: You should put a filter for identifier=18e06180 on your CAN socket\n", "WARNING: Captured no data.\n"]}], "source": ["help(CANSocket)"]}, {"cell_type": "code", "execution_count": 2, "id": "a986cb0f-278b-4e37-814d-d7378f8ef58e", "metadata": {"tags": []}, "outputs": [], "source": ["import warnings\n", "#isoTpSocket = ISOTPSocket(‘can0’, tx_id=0x700, rx_id=0x780,padding=True, filters=[{“can_id”: 0x780, rx_queue_size=1024\n", "# isoTpSocket = ISOTPNativeSocket('can0', tx_id=0x7B4, rx_id=0x734,padding=True,fd=True)\n", "\n", "\n", "with warnings.catch_warnings():\n", "    try:\n", "        warnings.simplefilter(\"error\", category=UserWarning)\n", "        isoTpSocket = ISOTPSocket(CANSocket(bustype='socketcan', channel=\"can0\",fd=False,can_filters=[{'can_id': 0x72b, 'can_mask': 0x77F}]),tx_id=0x72b, rx_id=0x7ab,padding=True,basecls=ISOTP)\n", "# isoTpSocket = ISOTPSocket('can0',tx_id=0x00000758, rx_id=0x000007d8,padding=True)\n", "    except FileNotFoundError as e:\n", "        print('aa')\n", "#isoTpSocket = ISOTPSocket(CANSocket(bustype='socketcan',fd=True,channel='can0'),tx_id=0x758, rx_id=0x7d8,padding=True)\n", "# isoTpMessage = ISOTP(binascii.a2b_hex('1001'))\n", "# while True:\n", "#     message = isoTpSocket.recv()\n", "#     print(message)\n", "    \n", "# pkg = isoTpSocket.sr1(isoTpMessage,timeout=5,verbose=0)"]}, {"cell_type": "code", "execution_count": 5, "id": "9ef4f750-990f-4c16-adc4-9e2768035944", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["<Sniffed: Other:0>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["def stop_sniff(packet):\n", "    return True  # 捕获到一个数据包后停止\n", "isoTpSocket.sniff(timeout=0.1, prn=stop_sniff, count=1)"]}, {"cell_type": "code", "execution_count": 5, "id": "98076627-c858-4d8f-ae85-499177fdf236", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on method sniff in module scapy.supersocket:\n", "\n", "sniff(*args, **kargs) method of scapy.contrib.isotp.isotp_soft_socket.ISOTPSoftSocket instance\n", "\n"]}], "source": ["help(isoTpSocket.sniff)"]}, {"cell_type": "code", "execution_count": 15, "id": "da8274fc-a9ed-4c48-8b77-f570b8bf6cef", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING: You should put a filter for identifier=7ab on your CAN socket\n"]}, {"ename": "TypeError", "evalue": "recv() got an unexpected keyword argument 'timeout'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[15], line 4\u001b[0m\n\u001b[1;32m      2\u001b[0m isoTpMessage \u001b[38;5;241m=\u001b[39m ISOTP(sendData)\n\u001b[1;32m      3\u001b[0m isoTpSocket\u001b[38;5;241m.\u001b[39msend(isoTpMessage)\n\u001b[0;32m----> 4\u001b[0m \u001b[43misoTpSocket\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrecv\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m0.1\u001b[39;49m\u001b[43m)\u001b[49m\n", "\u001b[0;31mTypeError\u001b[0m: recv() got an unexpected keyword argument 'timeout'"]}], "source": ["sendData = binascii.a2b_hex('1003')\n", "isoTpMessage = ISOTP(sendData)\n", "isoTpSocket.send(isoTpMessage)\n", "isoTpSocket.recv(timeout=0.1)"]}, {"cell_type": "code", "execution_count": 10, "id": "51723bcb-dade-4dc5-915c-8a2f4b60fb31", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on function recv in module scapy.contrib.isotp.isotp_soft_socket:\n", "\n", "recv(self, x=65535)\n", "\n"]}], "source": ["help(ISOTPSocket.recv)"]}, {"cell_type": "code", "execution_count": 6, "id": "3bc02876-48a9-4fcc-9449-235c70bf4ace", "metadata": {"tags": []}, "outputs": [{"ename": "TypeError", "evalue": "recv() got an unexpected keyword argument 'timeout'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[6], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43misoTpSocket\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrecv\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m0.1\u001b[39;49m\u001b[43m)\u001b[49m\n", "\u001b[0;31mTypeError\u001b[0m: recv() got an unexpected keyword argument 'timeout'"]}], "source": ["isoTpSocket.recv(timeout=0.1)"]}, {"cell_type": "code", "execution_count": 4, "id": "b908693c-d8c3-463e-a5ba-489aa1a78791", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on method recv_raw in module scapy.contrib.isotp.isotp_soft_socket:\n", "\n", "recv_raw(x=65535) method of scapy.contrib.isotp.isotp_soft_socket.ISOTPSoftSocket instance\n", "    Receive a complete ISOTP message, blocking until a message is\n", "    received or the specified timeout is reached.\n", "    If self.timeout is 0, then this function doesn't block and returns the\n", "    first frame in the receive buffer or None if there isn't any.\n", "\n"]}], "source": ["help(isoTpSocket.recv_raw)"]}, {"cell_type": "code", "execution_count": null, "id": "aeb9c5e4-04ec-4c32-8e13-a93864dadf05", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Recv: 2ef1901111cccc\n", "send:  6ef190\n", "Recv: 6ef190cccccccc\n", "Recv: 2ef1901111cccc\n", "send:  6ef190\n", "Recv: 6ef190cccccccc\n", "Recv: 0b2ef1a3111111\n", "Recv: 0000cccccccccc\n", "Recv: 0000cccccccccc\n", "Recv: 0000cccccccccc\n", "Recv: 1111111111cccc\n", "Recv: 2ef1a41111cccc\n", "send:  7f2e7f\n", "Recv: 7f2e7fcccccccc\n", "Recv: 2ef1a51111cccc\n", "send:  7f2e7f\n", "Recv: 7f2e7fcccccccc\n", "Recv: 2ef1a711cccccc\n", "send:  7f2e7f\n", "Recv: 7f2e7fcccccccc\n", "Recv: 2ef1a81111cccc\n", "send:  7f2e7f\n", "Recv: 7f2e7fcccccccc\n", "Recv: 2ef1a91111cccc\n", "send:  7f2e7f\n", "Recv: 7f2e7fcccccccc\n", "Recv: 2ef1aa1111cccc\n", "send:  7f2e7f\n", "Recv: 7f2e7fcccccccc\n", "Recv: 2ef1f011cccccc\n", "send:  7f2e7f\n", "Recv: 7f2e7fcccccccc\n", "Recv: 0d2ef1f3111111\n", "Recv: 0000cccccccccc\n", "Recv: 11111111111111\n", "Recv: 0000cccccccccc\n", "Recv: 0000cccccccccc\n", "Recv: 0b2ef1f5111111\n", "Recv: 0000cccccccccc\n", "Recv: 0000cccccccccc\n", "Recv: 0000cccccccccc\n", "Recv: 1111111111cccc\n", "Recv: 132ef1f6111111\n", "Recv: 0000cccccccccc\n", "Recv: 0000cccccccccc\n", "Recv: 0000cccccccccc\n", "Recv: 11111111111111\n", "Recv: 111111111111cc\n", "Recv: 2ef1f71111cccc\n", "send:  7f2e7f\n", "Recv: 7f2e7fcccccccc\n", "Recv: 0b2ef1f9111111\n", "Recv: 0000cccccccccc\n", "Recv: 0000cccccccccc\n", "Recv: 0000cccccccccc\n", "Recv: 1111111111cccc\n", "Recv: 2ef1fc11cccccc\n", "send:  7f2e7f\n", "Recv: 7f2e7fcccccccc\n", "Recv: 432ef210111111\n", "Recv: 0000cccccccccc\n", "Recv: 0000cccccccccc\n", "Recv: 0000cccccccccc\n", "Recv: 11111111111111\n", "Recv: 11111111111111\n", "Recv: 11111111111111\n", "Recv: 11111111111111\n", "Recv: 11111111111111\n", "Recv: 11111111111111\n", "Recv: 11111111111111\n", "Recv: 11111111111111\n", "Recv: 1111111111cccc\n", "Recv: 2ef1901111cccc\n", "send:  6ef190\n", "Recv: 6ef190cccccccc\n", "Recv: 0b2ef1a3111111\n", "Recv: 0000cccccccccc\n", "Recv: 0000cccccccccc\n", "Recv: 0000cccccccccc\n", "Recv: 1111111111cccc\n", "Recv: 2ef1a41111cccc\n", "send:  7f2e7f\n", "Recv: 7f2e7fcccccccc\n", "Recv: 2ef1a51111cccc\n", "send:  7f2e7f\n", "Recv: 7f2e7fcccccccc\n", "Recv: 2ef1a711cccccc\n", "send:  7f2e7f\n", "Recv: 7f2e7fcccccccc\n", "Recv: 2ef1a81111cccc\n", "send:  7f2e7f\n", "Recv: 7f2e7fcccccccc\n", "Recv: 2ef1a91111cccc\n", "send:  7f2e7f\n", "Recv: 7f2e7fcccccccc\n", "Recv: 2ef1aa1111cccc\n", "send:  7f2e7f\n", "Recv: 7f2e7fcccccccc\n", "Recv: 2ef1f011cccccc\n", "send:  7f2e7f\n", "Recv: 7f2e7fcccccccc\n", "Recv: 0d2ef1f3111111\n", "Recv: 0000cccccccccc\n", "Recv: 0000cccccccccc\n", "Recv: 0000cccccccccc\n", "Recv: 11111111111111\n", "Recv: 0b2ef1f5111111\n", "Recv: 0000cccccccccc\n", "Recv: 0000cccccccccc\n", "Recv: 0000cccccccccc\n", "Recv: 1111111111cccc\n", "Recv: 132ef1f6111111\n", "Recv: 0000cccccccccc\n", "Recv: 0000cccccccccc\n", "Recv: 0000cccccccccc\n", "Recv: 11111111111111\n", "Recv: 111111111111cc\n", "Recv: 2ef1f71111cccc\n", "send:  7f2e7f\n", "Recv: 7f2e7fcccccccc\n", "Recv: 0b2ef1f9111111\n", "Recv: 0000cccccccccc\n", "Recv: 0000cccccccccc\n", "Recv: 0000cccccccccc\n", "Recv: 1111111111cccc\n", "Recv: 2ef1fc11cccccc\n", "send:  7f2e7f\n", "Recv: 7f2e7fcccccccc\n", "Recv: 432ef210111111\n", "Recv: 0000cccccccccc\n", "Recv: 0000cccccccccc\n", "Recv: 0000cccccccccc\n", "Recv: 11111111111111\n", "Recv: 11111111111111\n", "Recv: 11111111111111\n", "Recv: 11111111111111\n", "Recv: 11111111111111\n", "Recv: 11111111111111\n", "Recv: 11111111111111\n", "Recv: 11111111111111\n", "Recv: 1111111111cccc\n"]}], "source": ["import can\n", "import binascii\n", "from scapy.layers.can import CAN\n", "from scapy.contrib.isotp import ISOTPSocket\n", "from scapy.contrib.isotp import ISOTPNativeSocket\n", "from scapy.contrib.automotive.uds_scan import * \n", "from scapy.layers.can import *\n", "from scapy.main import load_contrib\n", "import signal\n", "import threading\n", "from reprint import output\n", "from scapy.contrib.cansocket import CANSocket\n", "from scapy.config import conf\n", "from scapy.all import raw\n", "import secrets\n", "\n", "conf.contribs['ISOTP'] = {'use-can-isotp-kernel-module': False}\n", "conf.contribs['CANSocket'] = {'use-python-can': True}\n", "load_contrib('automotive.uds')\n", "load_contrib('isotp')\n", "def gen<PERSON><PERSON><PERSON>(length):\n", "    random_bytes = secrets.token_bytes(length)\n", "    hex_string = secrets.token_hex(length)\n", "    return hex_string\n", "\n", "def Bitmask(cid, sid):\n", "    return cid ^ sid ^ 0x7ff\n", "\n", "def genSocket():\n", "    socketpool = []\n", "    with open('./data/udsid.txt','r') as f:\n", "        data = f.read()\n", "        destinationlist = [int(x.split()[0],16) for x in data.strip().split('\\n')]\n", "        sourcelist = [int(x.split()[1],16) for x in data.strip().split('\\n')]\n", "    for i in range(len(sourcelist)):\n", "        isoTpSocket = ISOTPSocket(CANSocket(bustype='socketcan', channel='can0',fd=False,\n", "                                            can_filters=[{'can_id': sourcelist[i], 'can_mask': Bitmask(sourcelist[i],destinationlist[i])},\n", "                                                        {'can_id': destinationlist[i], 'can_mask': Bitmask(sourcelist[i],destinationlist[i])}]),\n", "                                                        tx_id=sourcelist[i], rx_id=destinationlist[i],padding=True,basecls=ISOTP)\n", "        isoTpSocket.outs.filter_warning_emitted = True\n", "        socketpool.append(isoTpSocket)\n", "    return socketpool\n", "\n", "def receive_and_reply(channel):\n", "    socketpool = genSocket()\n", "    can_socket = CANSocket(bustype='socketcan', channel='can0')\n", "    socket = None\n", "    while True:\n", "        isoTpMessage = None\n", "        message = can_socket.recv()\n", "        uds_date = None\n", "        if message:\n", "            for each in socketpool:\n", "                if message.identifier == each.rx_id:\n", "                    socket = each\n", "            uds_data = UDS(message.data[1:])\n", "            print('Recv:', binascii.b2a_hex(raw(uds_data)).decode('utf-8'))\n", "\n", "        # 判断接收到的CAN ID是否为1003\n", "        if uds_data and uds_data[UDS].service == 0x10 and uds_data[UDS_DSC].diagnosticSessionType == 0x3:\n", "            sendData = binascii.a2b_hex('5003f12a3212')\n", "            isoTpMessage = ISOTP(sendData)\n", "            socket.send(isoTpMessage)\n", "\n", "        elif uds_data and uds_data[UDS].service == 0x10 and uds_data[UDS_DSC].diagnosticSessionType == 0x2:\n", "            sendData = binascii.a2b_hex('7f1078')\n", "            isoTpMessage = ISOTP(sendData)\n", "            socket.send(isoTpMessage)\n", "\n", "        elif uds_data and uds_data[UDS].service == 0x27 and uds_data[UDS_SA].securityAccessType == 0x1:\n", "            # 回复数据：ID=500, 数据=[0x31, 0x12, 0x23, 0x44]\n", "            sendData = binascii.a2b_hex('6701'+genR<PERSON>om(4))\n", "            isoTpMessage = ISOTP(sendData)\n", "            socket.send(isoTpMessage)\n", "\n", "        elif uds_data and uds_data[UDS].service == 0x27 and uds_data[UDS_SA].securityAccessType == 0x3:\n", "            # 回复数据：ID=500, 数据=[0x31, 0x12, 0x23, 0x44]\n", "            sendData = binascii.a2b_hex('7f277f')\n", "            isoTpMessage = ISOTP(sendData)\n", "            socket.send(isoTpMessage)\n", "        \n", "        elif uds_data and uds_data[UDS].service == 0x2e and uds_data[UDS_WDBI].dataIdentifier == 0xf190:\n", "            sendData = binascii.a2b_hex('6ef190')\n", "            isoTpMessage = ISOTP(sendData)\n", "            socket.send(isoTpMessage)\n", "        \n", "        elif uds_data and uds_data[UDS].service == 0x2e:\n", "            # 回复数据：ID=500, 数据=[0x31, 0x12, 0x23, 0x44]\n", "            sendData = binascii.a2b_hex('7f2e7f')\n", "            isoTpMessage = ISOTP(sendData)\n", "            socket.send(isoTpMessage)\n", "\n", "        \n", "\n", "        elif uds_data and uds_data[UDS].service == 0x22 and uds_data[UDS_RDBI].identifiers[0] == 0xf190:\n", "            sendData = binascii.a2b_hex('62f1904b4d3841363530373747414c3132373838')\n", "            isoTpMessage = ISOTP(sendData)\n", "            socket.send(isoTpMessage)\n", "\n", "        elif uds_data and uds_data[UDS].service == 0x22 and uds_data[UDS_RDBI].identifiers[0] == 0xf191:\n", "            sendData = binascii.a2b_hex('7f2231')\n", "            isoTpMessage = ISOTP(sendData)\n", "            socket.send(isoTpMessage)\n", "\n", "        elif uds_data and uds_data[UDS].service == 0x22 and uds_data[UDS_RDBI].identifiers[0] == 0xf192:\n", "            sendData = binascii.a2b_hex('7f2235')\n", "            isoTpMessage = ISOTP(sendData)\n", "            socket.send(isoTpMessage)\n", "        if isoTpMessage: \n", "            print('send: ',binascii.b2a_hex(raw(isoTpMessage)).decode('utf-8'))\n", "if __name__ == \"__main__\":\n", "    # 设置虚拟CAN通道\n", "    vcan_channel = 'can0'\n", "\n", "    # 接收并回复CAN数据\n", "    ss = receive_and_reply(vcan_channel)\n"]}, {"cell_type": "code", "execution_count": 27, "id": "a3450c83-cbe2-4ca3-b595-e71059903dd3", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["<ISOTP  |>"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["ISOTP()"]}, {"cell_type": "code", "execution_count": 24, "id": "d7c98376-cbb3-46ed-adad-afc98513612d", "metadata": {"tags": []}, "outputs": [], "source": ["def genSocket():\n", "    socketpool = []\n", "    with open('./data/udsid.txt','r') as f:\n", "        data = f.read()\n", "        destinationlist = [int(x.split()[0],16) for x in data.strip().split('\\n')]\n", "        sourcelist = [int(x.split()[1],16) for x in data.strip().split('\\n')]\n", "    for i in range(len(sourcelist)):\n", "        isoTpSocket = ISOTPSocket(CANSocket(bustype='socketcan', channel='can0',fd=False,\n", "                                            can_filters=[{'can_id': sourcelist[i], 'can_mask': Bitmask(sourcelist[i],destinationlist[i])},\n", "                                                        {'can_id': destinationlist[i], 'can_mask': Bitmask(sourcelist[i],destinationlist[i])}]),\n", "                                                        tx_id=sourcelist[i], rx_id=destinationlist[i],padding=True,basecls=ISOTP)\n", "        isoTpSocket.outs.filter_warning_emitted = True\n", "        socketpool.append(isoTpSocket)\n", "    return socketpool"]}, {"cell_type": "code", "execution_count": 25, "id": "137f1d9d-025c-4fd7-b1a7-1607b0f63c7e", "metadata": {"tags": []}, "outputs": [], "source": ["s = genSocket()"]}, {"cell_type": "code", "execution_count": 26, "id": "38e06481-6ae1-42bf-aab1-4099de6fea03", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["[<<ISOTPSoftSocket> at 0x7fe571ead0a0>,\n", " <<ISOTPSoftSocket> at 0x7fe571cb1af0>,\n", " <<ISOTPSoftSocket> at 0x7fe571987f70>,\n", " <<ISOTPSoftSocket> at 0x7fe571987d60>,\n", " <<ISOTPSoftSocket> at 0x7fe5718f2130>,\n", " <<ISOTPSoftSocket> at 0x7fe571894250>,\n", " <<ISOTPSoftSocket> at 0x7fe571894e80>,\n", " <<ISOTPSoftSocket> at 0x7fe571894610>]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["s"]}, {"cell_type": "code", "execution_count": 39, "id": "d6df114f-32d8-4507-988d-99d65b74bdd8", "metadata": {"tags": []}, "outputs": [], "source": ["uds_data = UDS(binascii.a2b_hex('22f1901122334455667788'))"]}, {"cell_type": "code", "execution_count": 40, "id": "79a6c4c5-3e22-424c-9b17-d8e72c9067ce", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["<UDS  service=ReadDataByIdentifier |<UDS_RDBI  identifiers=[0xf190, 0x1122, 0x3344, 0x5566, 0x7788] |>>"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["uds_data"]}, {"cell_type": "code", "execution_count": 25, "id": "c6cf2b89-2afc-48f0-8273-3003e9011911", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": 13, "id": "953d281f-5537-4c30-8c8d-c73bc801b14e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CAN\n"]}], "source": ["import can\n", "import binascii\n", "from scapy.layers.can import CAN\n", "from scapy.contrib.isotp import ISOTPSocket\n", "from scapy.contrib.isotp import ISOTPNativeSocket\n", "from scapy.contrib.automotive.uds_scan import * \n", "from scapy.layers.can import *\n", "from scapy.main import load_contrib\n", "import signal\n", "import threading\n", "from reprint import output\n", "from scapy.contrib.cansocket import CANSocket\n", "from scapy.config import conf\n", "from scapy.all import raw\n", "import secrets\n", "\n", "conf.contribs['ISOTP'] = {'use-can-isotp-kernel-module': False}\n", "conf.contribs['CANSocket'] = {'use-python-can': True}\n", "load_contrib('automotive.uds')\n", "load_contrib('isotp')\n", "\n", "\n", "def receive_and_reply(channel):\n", "    bus = CANSocket(bustype='socketcan', channel='can0')\n", "\n", "    while True:\n", "        message = bus.recv()\n", "        print(message)\n", "        return message\n", "\n", "if __name__ == \"__main__\":\n", "    # 设置虚拟CAN通道\n", "    vcan_channel = 'vcan0'\n", "\n", "    # 接收并回复CAN数据\n", "    message = receive_and_reply(vcan_channel)\n"]}, {"cell_type": "code", "execution_count": 36, "id": "cf7cd9d8-fa02-4511-8aa4-4fa443039cc4", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["b'\\x10\\x03\\xcc\\xcc\\xcc\\xcc\\xcc'"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": null, "id": "11a781a1-e1ec-427e-bda8-7936389ecb65", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Recv: 22f190\n", "send:  62f1904b4d3841363530373747414c3132373838\n"]}], "source": ["import can\n", "import binascii\n", "from scapy.layers.can import CAN\n", "from scapy.contrib.isotp import ISOTPSocket\n", "from scapy.contrib.isotp import ISOTPNativeSocket\n", "from scapy.contrib.automotive.uds_scan import * \n", "from scapy.layers.can import *\n", "from scapy.main import load_contrib\n", "import signal\n", "import threading\n", "from reprint import output\n", "from scapy.contrib.cansocket import CANSocket\n", "from scapy.config import conf\n", "from scapy.all import raw\n", "import secrets\n", "\n", "conf.contribs['ISOTP'] = {'use-can-isotp-kernel-module': False}\n", "conf.contribs['CANSocket'] = {'use-python-can': True}\n", "load_contrib('automotive.uds')\n", "load_contrib('isotp')\n", "def gen<PERSON><PERSON><PERSON>(length):\n", "    random_bytes = secrets.token_bytes(length)\n", "    hex_string = secrets.token_hex(length)\n", "    return hex_string\n", "\n", "def Bitmask(cid, sid):\n", "    return cid ^ sid ^ 0x7ff\n", "\n", "def genSocket():\n", "    \n", "    destinationlist = [0x400]\n", "    sourcelist = [0x500]\n", "    isoTpSocket = ISOTPSocket(CANSocket(bustype='socketcan', channel='can0',fd=False,\n", "                                        can_filters=[{'can_id': sourcelist[0], 'can_mask': Bitmask(sourcelist[0],destinationlist[0])},\n", "                                                    {'can_id': destinationlist[0], 'can_mask': Bitmask(sourcelist[0],destinationlist[0])}]),\n", "                                                    tx_id=sourcelist[0], rx_id=destinationlist[0],padding=True,basecls=ISOTP)\n", "    isoTpSocket.outs.filter_warning_emitted = True\n", "    return isoTpSocket\n", "\n", "def receive_and_reply(channel):\n", "    socket = genSocket()\n", "    while True:\n", "        isoTpMessage = None\n", "        message = socket.recv()\n", "        uds_date = None\n", "        if message:\n", "            uds_data = UDS(message.data)\n", "            print('Recv:', binascii.b2a_hex(raw(uds_data)).decode('utf-8'))\n", "\n", "        # 判断接收到的CAN ID是否为1003\n", "        if uds_data and uds_data[UDS].service == 0x10 and uds_data[UDS_DSC].diagnosticSessionType == 0x3:\n", "            for i in range(5):\n", "                sendData = binascii.a2b_hex('7f1078')\n", "                isoTpMessage = ISOTP(sendData)\n", "                socket.send(isoTpMessage)\n", "                time.sleep(0.1)\n", "            # sendData = binascii.a2b_hex('5003f12a3212')\n", "            # isoTpMessage = ISOTP(sendData)\n", "            # socket.send(isoTpMessage)\n", "\n", "        elif uds_data and uds_data[UDS].service == 0x10 and uds_data[UDS_DSC].diagnosticSessionType == 0x2:\n", "            sendData = binascii.a2b_hex('7f1078')\n", "            isoTpMessage = ISOTP(sendData)\n", "            socket.send(isoTpMessage)\n", "        elif uds_data and uds_data[UDS].service == 0x10 and uds_data[UDS_DSC].diagnosticSessionType == 0x2:\n", "            sendData = binascii.a2b_hex('7f1078')\n", "            isoTpMessage = ISOTP(sendData)\n", "            socket.send(isoTpMessage)\n", "\n", "        elif uds_data and uds_data[UDS].service == 0x27 and uds_data[UDS_SA].securityAccessType == 0x1:\n", "            # 回复数据：ID=500, 数据=[0x31, 0x12, 0x23, 0x44]\n", "            time.sleep(4)\n", "            sendData = binascii.a2b_hex('6701'+genR<PERSON>om(4))\n", "            isoTpMessage = ISOTP(sendData)\n", "            socket.send(isoTpMessage)\n", "\n", "        elif uds_data and uds_data[UDS].service == 0x27 and uds_data[UDS_SA].securityAccessType == 0x3:\n", "            # 回复数据：ID=500, 数据=[0x31, 0x12, 0x23, 0x44]\n", "            sendData = binascii.a2b_hex('7f277f')\n", "            isoTpMessage = ISOTP(sendData)\n", "            socket.send(isoTpMessage)\n", "\n", "        elif uds_data and uds_data[UDS].service == 0x2e and uds_data[UDS_WDBI].dataIdentifier == 0xf190:\n", "            sendData = binascii.a2b_hex('6ef190')\n", "            isoTpMessage = ISOTP(sendData)\n", "            socket.send(isoTpMessage)\n", "\n", "        elif uds_data and uds_data[UDS].service == 0x22 and uds_data[UDS_RDBI].identifiers[0] == 0xf190:\n", "            sendData = binascii.a2b_hex('62f1904b4d3841363530373747414c3132373838')\n", "            isoTpMessage = ISOTP(sendData)\n", "            socket.send(isoTpMessage)\n", "\n", "        elif uds_data and uds_data[UDS].service == 0x22 and uds_data[UDS_RDBI].identifiers[0] == 0xf191:\n", "            sendData = binascii.a2b_hex('7f2231')\n", "            isoTpMessage = ISOTP(sendData)\n", "            socket.send(isoTpMessage)\n", "\n", "        elif uds_data and uds_data[UDS].service == 0x22 and uds_data[UDS_RDBI].identifiers[0] == 0xf192:\n", "            sendData = binascii.a2b_hex('7f2235')\n", "            isoTpMessage = ISOTP(sendData)\n", "            socket.send(isoTpMessage)\n", "        if isoTpMessage: \n", "            print('send: ',binascii.b2a_hex(raw(isoTpMessage)).decode('utf-8'))\n", "if __name__ == \"__main__\":\n", "    # 设置虚拟CAN通道\n", "    vcan_channel = 'can0'\n", "\n", "    # 接收并回复CAN数据\n", "    ss = receive_and_reply(vcan_channel)\n"]}, {"cell_type": "code", "execution_count": 27, "id": "2b1d617e-3f83-4179-b577-7b1766627b39", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["+----------------+----------------+\n", "|      Send      |    Received    |\n", "+----------------+----------------+\n", "|    0x18daf1e6  |    0x18daf1e6  |\n", "|    0x18daf1e6  |    0x18daf1e6  |\n", "+----------------+----------------+\n"]}], "source": ["# source = ['0x700','0x800']\n", "# target = ['0x708','0x808']\n", "source = ['0x18daf1e6','0x18daf1e6']\n", "target = ['0x18daf1e6','0x18daf1e6']\n", "print('+----------------+----------------+')\n", "print(\"|      {:10}|    {:10}  |\".format('Send', 'Received'))\n", "print('+----------------+----------------+')\n", "for i in range(len(source)):\n", "    print(\"|    {:10}  |    {:10}  |\".format(source[i], target[i]))\n", "print('+----------------+----------------+')"]}, {"cell_type": "code", "execution_count": 1, "id": "e9518ea2-9b6f-41a3-ab54-55f4c278b717", "metadata": {"tags": []}, "outputs": [], "source": ["import can\n", "import binascii\n", "from scapy.layers.can import CAN\n", "from scapy.contrib.isotp import ISOTPSocket\n", "from scapy.contrib.isotp import ISOTPNativeSocket\n", "from scapy.contrib.automotive.uds_scan import * \n", "from scapy.layers.can import *\n", "from scapy.main import load_contrib\n", "import signal\n", "import threading\n", "from reprint import output\n", "from scapy.contrib.cansocket import CANSocket\n", "from scapy.config import conf\n", "from scapy.all import raw\n", "import secrets\n", "\n", "conf.contribs['ISOTP'] = {'use-can-isotp-kernel-module': False}\n", "conf.contribs['CANSocket'] = {'use-python-can': True}\n", "load_contrib('automotive.uds')\n", "load_contrib('isotp')"]}, {"cell_type": "code", "execution_count": null, "id": "2a2e94bd-5a07-4d3f-a444-663eda8217b3", "metadata": {"tags": []}, "outputs": [], "source": ["\n", "def gen<PERSON><PERSON><PERSON>(length):\n", "    random_bytes = secrets.token_bytes(length)\n", "    hex_string = secrets.token_hex(length)\n", "    return hex_string\n", "\n", "def Bitmask(cid, sid):\n", "    return cid ^ sid ^ 0x7ff\n", "\n", "def genSocket():\n", "    \n", "    destinationlist = 0x612\n", "    sourcelist = 0x692\n", "    isoTpSocket = ISOTPSocket(CANSocket(bustype='socketcan', channel='can0',fd=False,\n", "                                        can_filters=[{'can_id': sourcelist, 'can_mask': Bitmask(sourcelist,destinationlist)},\n", "                                                    {'can_id': destinationlist, 'can_mask': Bitmask(sourcelist,destinationlist)}]),\n", "                                                    tx_id=sourcelist, rx_id=destinationlist,padding=True,basecls=ISOTP)\n", "    isoTpSocket.outs.filter_warning_emitted = True\n", "    \n", "    return isoTpSocket\n", "\n", "def receive_and_reply(channel):\n", "    socket = genSocket()\n", "    while True:\n", "        resp = socket.recv()\n", "        udsdata = UDS(resp.data)\n", "        \n", "        if udsdata.service == 0x10 and udsdata.diagnosticSessionType == 0x03:\n", "            socket.send(ISOTP(binascii.a2b_hex('500311223344')))\n", "        elif udsdata.service == 0x10 and udsdata.diagnosticSessionType == 0x02:\n", "            socket.send(ISOTP(binascii.a2b_hex('500211223344')))\n", "            \n", "            \n", "        elif udsdata.service == 0x10:\n", "            socket.send(ISOTP(binascii.a2b_hex('7f1022')))\n", "        elif udsdata.service == 0x11:\n", "            socket.send(ISOTP(binascii.a2b_hex('7f1122')))\n", "        elif udsdata.service == 0x22:\n", "            socket.send(ISOTP(binascii.a2b_hex('7f2222')))\n", "        \n", "        elif udsdata.service == 0x22 and 0xf1ff in udsdata.identifiers:\n", "            socket.send(ISOTP(binascii.a2b_hex('7f2278')))\n", "            time.sleep(5)\n", "            socket.send(ISOTP(binascii.a2b_hex('7f2278')))\n", "            time.sleep(5)\n", "            socket.send(ISOTP(binascii.a2b_hex('7f2278')))\n", "            time.sleep(5)\n", "            socket.send(ISOTP(binascii.a2b_hex('62f1f1112233445566778899001122334455667788990011223344556677889900')))\n", "            time.sleep(5)\n", "            socket.send(ISOTP(binascii.a2b_hex('62f1ff112233445566778899001122334455667788990011223344556677889900')))\n", "        \n", "        elif udsdata.service == 0x27 and udsdata.securityAccessType == 0x01:\n", "            socket.send(ISOTP(binascii.a2b_hex('6701'+genRandom(128))))\n", "            \n", "    return udsdata\n", "    \n", "if __name__ == \"__main__\":\n", "    # 设置虚拟CAN通道\n", "    vcan_channel = 'can0'\n", "\n", "    # 接收并回复CAN数据\n", "    ss = receive_and_reply(vcan_channel)\n"]}, {"cell_type": "code", "execution_count": 7, "id": "f8e93cda-f1ff-49c4-b2a8-ce8d48c39d76", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["'f62eccf2'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["genRandom(4)"]}, {"cell_type": "code", "execution_count": 2, "id": "f175da66-dad6-4b45-8d72-ab17377d7d31", "metadata": {"tags": []}, "outputs": [], "source": ["socket = genSocket()\n", "resp = socket.sniff(timeout=0.4)"]}, {"cell_type": "code", "execution_count": 4, "id": "4c5489e5-3ca1-4b09-9fcf-0b7003919276", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["resp.res"]}, {"cell_type": "code", "execution_count": null, "id": "4e86e98d-2dda-433a-a467-10f3ca70a1b2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 5, "id": "4b81f514-2f48-45b0-97b4-c75d83dcbe61", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["<UDS  service=SecurityAccess |<UDS_SA  securityAccessType=1 |>>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["UDS(binascii.a2b_hex('2701'))"]}, {"cell_type": "code", "execution_count": 38, "id": "9c3fc1c3-f231-48bf-98f4-f46b50389fa7", "metadata": {"tags": []}, "outputs": [], "source": ["sendData = binascii.a2b_hex('310133bb001122')"]}, {"cell_type": "code", "execution_count": 42, "id": "536ffa55-73c4-42e2-857c-55d00f95ef89", "metadata": {"tags": []}, "outputs": [], "source": ["uds_data = UDS(binascii.a2b_hex('710133bc00'))"]}, {"cell_type": "code", "execution_count": 43, "id": "0fc0a97d-4273-4b05-8602-b8ec27b74a7a", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["<UDS  service=RoutineControlPositiveResponse |<UDS_RCPR  routineControlType=startRoutine routineIdentifier=0x33bc |<Raw  load='\\x00' |>>>"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["uds_data"]}, {"cell_type": "code", "execution_count": 44, "id": "dd708fd9-c0f4-428d-83ae-0cec55f8da46", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["<UDS  service=RoutineControl |<UDS_RC  routineControlType=startRoutine routineIdentifier=0x33bb |<Raw  load='\\x00\\x11\"' |>>>"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["UDS(sendData)"]}, {"cell_type": "code", "execution_count": 45, "id": "3658cc43-0517-4e15-8a4f-6b37dee6b1d5", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["aa\n"]}], "source": []}, {"cell_type": "code", "execution_count": 4, "id": "df9fb46f-ec5e-4999-be70-6a5c120214dd", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on function sniff in module scapy.supersocket:\n", "\n", "sniff(self, *args, **kargs)\n", "\n"]}], "source": ["help(ISOTPSocket.sniff)"]}, {"cell_type": "markdown", "id": "9ee8977c-4b00-482c-b4d4-483a665e1f7d", "metadata": {}, "source": ["# 德赛西威"]}, {"cell_type": "code", "execution_count": 1, "id": "bb860ca9-8888-4b75-a538-98e0c390c268", "metadata": {"tags": []}, "outputs": [], "source": ["import cmd2\n", "import sys\n", "import os\n", "from utils.scanUDSid import *\n", "import getopt\n", "import logging\n", "import binascii\n", "from scapy.contrib.cansocket import CANSocket\n", "from scapy.config import conf\n", "import json\n", "import time\n", "from scapy.layers.can import CAN\n", "from scapy.contrib.isotp import ISOTPSocket\n", "from scapy.contrib.automotive.uds_scan import * \n", "from scapy.layers.can import *\n", "from scapy.main import load_contrib\n", "from reprint import output\n", "conf.contribs['ISOTP'] = {'use-can-isotp-kernel-module': False}\n", "conf.contribs['CANSocket'] = {'use-python-can': True}\n", "load_contrib('isotp')\n", "load_contrib('automotive.uds')\n", "import can"]}, {"cell_type": "code", "execution_count": 2, "id": "2db82bf3-35ab-4f3e-b866-1782d0ab89c8", "metadata": {"tags": []}, "outputs": [], "source": ["def Bitmask(cid, sid):\n", "    if cid > 0x7ff or sid > 0x7ff:\n", "        return cid ^ sid ^ 0x1fffffff\n", "    else:\n", "        return cid ^ sid ^ 0x7ff"]}, {"cell_type": "code", "execution_count": 3, "id": "a8909262-c667-4c1f-a705-a257a9a712db", "metadata": {"tags": []}, "outputs": [], "source": ["tx = 0x18da60f1\n", "rx = 0x18daf160"]}, {"cell_type": "code", "execution_count": 4, "id": "067406f0-6ada-4159-99b4-5343024d36f7", "metadata": {"tags": []}, "outputs": [], "source": ["isoTpSocket = ISOTPSocket(CANSocket(bustype='socketcan', channel='can0',fd=False,\n", "                                                    can_filters=[{'can_id': tx, 'can_mask': Bitmask(tx,rx)},\n", "                                                    {'can_id': rx, 'can_mask': Bitmask(rx,tx)}]),\n", "                                                    tx_id=tx, rx_id=rx,padding=True,basecls=ISOTP)"]}, {"cell_type": "code", "execution_count": null, "id": "c2f0c458-c685-4494-aba5-2db8f8d9adbb", "metadata": {"tags": []}, "outputs": [], "source": ["can_interface = 'can0'  # 例如 socketcan 为 'can0'，或使用 'PCAN_USBBUS1'、'vector'\n", "bus = can.interface.Bus(channel=can_interface, bustype='socketcan')  # 更换为对应 bustype\n", "\n", "\n", "ext_id = 0x1E340003\n", "\n", "# 循环发送 0x80xx0600（xx 从 0x00 到 0xFF）\n", "for i in range(256):\n", "    data = [0x80, i, 0x06, 0x00]  # 补齐为 8 字节\n", "    msg = can.Message(arbitration_id=ext_id,\n", "                      data=data,\n", "                      is_extended_id=True)\n", "    isoTpMessage = ISOTP(binascii.a2b_hex('22f190'))\n", "    isoTpSocket.send(isoTpMessage)\n", "    time.sleep(0.01)\n", "    bus.send(msg)\n", "    pkg = isoTpSocket.sniff(timeout=0.1)\n", "    if len(pkg) > 0:\n", "        print(binascii.b2a_hex(pkg[1]).decode('utf-8'))\n", "       "]}, {"cell_type": "code", "execution_count": 7, "id": "5c766699-cfc9-48f0-8e0c-9f848b1bc9c7", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["<Sniffed: Other:0>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["isoTpSocket.sniff(timeout=0.4)"]}, {"cell_type": "code", "execution_count": null, "id": "1b3bcb09-2c14-4da2-9086-06b5508636df", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 5}