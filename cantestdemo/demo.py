import can
import binascii
from scapy.layers.can import CAN
from scapy.contrib.isotp import ISOTPSocket
from scapy.contrib.isotp import ISOTPNativeSocket
from scapy.contrib.automotive.uds_scan import * 
from scapy.layers.can import *
from scapy.main import load_contrib
import signal
import threading
from reprint import output
from scapy.contrib.cansocket import CANSocket
from scapy.config import conf
from scapy.all import raw

conf.contribs['ISOTP'] = {'use-can-isotp-kernel-module': False}
conf.contribs['CANSocket'] = {'use-python-can': True}
load_contrib('automotive.uds')
load_contrib('isotp')


def receive_and_reply(channel):
    flag = 0
    changed = 0
    bus = can.interface.Bus(channel=channel, bustype='socketcan')

    while True:
        message = bus.recv()
        print(f"Received CAN message: ID={message.arbitration_id}, Data={message.data}")
        aa = binascii.b2a_hex(message.data)
        print(aa)
        uds_data = UDS(binascii.a2b_hex(aa[2:6]))
        print(uds_data)

        # 判断接收到的CAN ID是否为1003
        if uds_data[UDS].service == 0x10 and uds_data[UDS_DSC].diagnosticSessionType == 3:
            # 回复数据：ID=500, 数据=[0x31, 0x12, 0x23, 0x44]
            reply_message = can.Message(arbitration_id=0x7ab, data=binascii.a2b_hex('065003f1342412'), is_extended_id = False)
            bus.send(reply_message)

        if uds_data[UDS].service == 0x10 and uds_data[UDS_DSC].diagnosticSessionType == 2:
            # 回复数据：ID=500, 数据=[0x31, 0x12, 0x23, 0x44]
            reply_message = can.Message(arbitration_id=0x7ab, data=binascii.a2b_hex('065002f1342412'), is_extended_id = False)
            bus.send(reply_message)
        if uds_data[UDS].service == 0x10 and uds_data[UDS_DSC].diagnosticSessionType == 68:
            # 回复数据：ID=500, 数据=[0x31, 0x12, 0x23, 0x44]
            reply_message = can.Message(arbitration_id=0x7ab, data=binascii.a2b_hex('065044f1342412CC'), is_extended_id = False)
            flag = 1
            bus.send(reply_message)
        if uds_data[UDS].service == 0x2E:
            # 回复数据：ID=500, 数据=[0x31, 0x12, 0x23, 0x44]
            if flag == 1:
                reply_message = can.Message(arbitration_id=0x7ab, data=binascii.a2b_hex('036ef156CCCCCCCC'), is_extended_id = False)
                bus.send(reply_message)
            else:
                reply_message = can.Message(arbitration_id=0x7ab, data=binascii.a2b_hex('037f2e33CCCCCCCC'), is_extended_id = False)
                bus.send(reply_message)
        if uds_data[UDS].service == 0x22:
            # 回复数据：ID=500, 数据=[0x31, 0x12, 0x23, 0x44]
            if changed == 1:
                reply_message = can.Message(arbitration_id=0x7ab, data=binascii.a2b_hex('0462f15601CCCCCC'), is_extended_id = False)
                bus.send(reply_message)
            else:
                reply_message = can.Message(arbitration_id=0x7ab, data=binascii.a2b_hex('0462f156AACCCCCC'), is_extended_id = False)
                bus.send(reply_message)
if __name__ == "__main__":
    # 设置虚拟CAN通道
    vcan_channel = 'can0'

    # 接收并回复CAN数据
    receive_and_reply(vcan_channel)
