import sys
import os
import binascii
from scapy.contrib.cansocket import CANSocket
from scapy.config import conf
from progress.bar import Bar
from scapy.all import raw
import json
import pickle
import secrets
import logging
import time
from scapy.layers.can import CAN
from scapy.contrib.isotp import ISOTPSocket
from scapy.contrib.isotp import ISOTPNativeSocket
from scapy.contrib.automotive.uds_scan import * 
from scapy.layers.can import *
from scapy.main import load_contrib
import scapy.layers.tuntap
import random
conf.contribs['ISOTP'] = {'use-can-isotp-kernel-module': True}
conf.contribs['CANSocket'] = {'use-python-can': False}
load_contrib('automotive.uds')
load_contrib('isotp')
global_p2_timeout = 0.1
global_p2_star_time = 5



negativeResponseCodes = {
    0x00: "POSITIVE_RESPONSE",
    0x10: "GENERAL_REJECT",
    0x11: "SERVICE_NOT_SUPPORTED",
    0x12: "SUB_FUNCTION_NOT_SUPPORTED",
    0x13: "INCORRECT_MESSAGE_LENGTH_OR_INVALID_FORMAT",
    0x14: "RESPONSE_TOO_LONG",
    0x21: "BUSY_REPEAT_REQUEST",
    0x22: "CONDITIONS_NOT_CORRECT",
    0x24: "REQUEST_SEQUENCE_ERROR",
    0x25: "NO_RESPONSE_FROM_SUBNET_COMPONENT",
    0x26: "FAILURE_PREVENTS_EXECUTION_OF_REQUESTED_ACTION",
    0x31: "REQUEST_OUT_OF_RANGE",
    0x33: "SECURITY_ACCESS_DENIED",
    0x35: "INVALID_KEY",
    0x36: "EXCEEDED_NUMBER_OF_ATTEMPTS",
    0x37: "REQUIRED_TIME_DELAY_NOT_EXPIRED",
    0x70: "UPLOAD_DOWNLOAD_NOT_ACCEPTED",
    0x71: "TRANSFER_DATA_SUSPENDED",
    0x72: "GENERAL_PROGRAMMING_FAILURE",
    0x73: "WRONG_BLOCK_SEQUENCE_COUNTER",
    0x78: "REQUEST_CORRECTLY_RECEIVED_RESPONSE_PENDING",
    0x7E: "SUB_FUNCTION_NOT_SUPPORTED_IN_ACTIVE_SESSION",
    0x7F: "SERVICE_NOT_SUPPORTED_IN_ACTIVE_SESSION",
    0x81: "RPM_TOO_HIGH",
    0x82: "RPM_TOO_LOW",
    0x83: "ENGINE_IS_RUNNING",
    0x84: "ENGINE_IS_NOT_RUNNING",
    0x85: "ENGINE_RUN_TIME_TOO_LOW",
    0x86: "TEMPERATURE_TOO_HIGH",
    0x87: "TEMPERATURE_TOO_LOW",
    0x88: "VEHICLE_SPEED_TOO_HIGH",
    0x89: "VEHICLE_SPEED_TOO_LOW",
    0x8A: "THROTTLE_PEDAL_TOO_HIGH",
    0x8B: "THROTTLE_PEDAL_TOO_LOW",
    0x8C: "TRANSMISSION_RANGE_NOT_IN_NEUTRAL",
    0x8D: "TRANSMISSION_RANGE_NOT_IN_GEAR",
    0x8F: "BRAKE_SWITCHES_NOT_CLOSED",
    0x90: "SHIFT_LEVER_NOT_IN_PARK",
    0x91: "TORQUE_CONVERTER_CLUTCH_LOCKED",
    0x92: "VOLTAGE_TOO_HIGH",
    0x93: "VOLTAGE_TOO_LOW",
    0x74:"ISO SAE Reserved",
    0x75:"ISO SAE Reserved",
    0x76:"ISO SAE Reserved",
    0x77:"ISO SAE Reserved",
    0x27:"ISO SAE Reserved",
    0x28:"ISO SAE Reserved",
    0x29:"ISO SAE Reserved",
    0x2a:"ISO SAE Reserved",
    0x2b:"ISO SAE Reserved",
    0x2c:"ISO SAE Reserved",
    0x2d:"ISO SAE Reserved",
    0x2e:"ISO SAE Reserved",
    0x2f:"ISO SAE Reserved",
    0x30:"ISO SAE Reserved",
    0x32:"ISO SAE Reserved",
    0x34:"ISO SAE Reserved",
    0x38:"Reserved By Extended Data Link Security Document",
    0x39:"Reserved By Extended Data Link Security Document",
    0x3a:"Reserved By Extended Data Link Security Document",
    0x3b:"Reserved By Extended Data Link Security Document",
    0x3c:"Reserved By Extended Data Link Security Document",
    0x3d:"Reserved By Extended Data Link Security Document",
    0x3e:"Reserved By Extended Data Link Security Document",
    0x3f:"Reserved By Extended Data Link Security Document",
    0x40:"Reserved By Extended Data Link Security Document",
    0x41:"Reserved By Extended Data Link Security Document",
    0x42:"Reserved By Extended Data Link Security Document",
    0x43:"Reserved By Extended Data Link Security Document",
    0x44:"Reserved By Extended Data Link Security Document",
    0x45:"Reserved By Extended Data Link Security Document",
    0x46:"Reserved By Extended Data Link Security Document",
    0x47:"Reserved By Extended Data Link Security Document",
    0x48:"Reserved By Extended Data Link Security Document",
    0x49:"Reserved By Extended Data Link Security Document",
    0x4a:"Reserved By Extended Data Link Security Document",
    0x4b:"Reserved By Extended Data Link Security Document",
    0x4c:"Reserved By Extended Data Link Security Document",
    0x4d:"Reserved By Extended Data Link Security Document",
    0x4e:"Reserved By Extended Data Link Security Document",
    0x4f:"Reserved By Extended Data Link Security Document",
    0x50:"ISO SAE Reserved",
    0x51:"ISO SAE Reserved",
    0x52:"ISO SAE Reserved",
    0x53:"ISO SAE Reserved",
    0x54:"ISO SAE Reserved",
    0x55:"ISO SAE Reserved",
    0x56:"ISO SAE Reserved",
    0x57:"ISO SAE Reserved",
    0x58:"ISO SAE Reserved",
    0x59:"ISO SAE Reserved",
    0x5a:"ISO SAE Reserved",
    0x5b:"ISO SAE Reserved",
    0x5c:"ISO SAE Reserved",
    0x5d:"ISO SAE Reserved",
    0x5e:"ISO SAE Reserved",
    0x5f:"ISO SAE Reserved",
    0x60:"ISO SAE Reserved",
    0x61:"ISO SAE Reserved",
    0x62:"ISO SAE Reserved",
    0x63:"ISO SAE Reserved",
    0x64:"ISO SAE Reserved",
    0x65:"ISO SAE Reserved",
    0x66:"ISO SAE Reserved",
    0x67:"ISO SAE Reserved",
    0x68:"ISO SAE Reserved",
    0x69:"ISO SAE Reserved",
    0x6a:"ISO SAE Reserved",
    0x6b:"ISO SAE Reserved",
    0x6c:"ISO SAE Reserved",
    0x6d:"ISO SAE Reserved",
    0x6e:"ISO SAE Reserved",
    0x6f:"ISO SAE Reserved",
    0x79:"ISO SAE Reserved",
    0x7a:"ISO SAE Reserved",
    0x7b:"ISO SAE Reserved",
    0x7c:"ISO SAE Reserved",
    0x7d:"ISO SAE Reserved",
    0x80:"ISO SAE Reserved",
    0x94:"Reserved For Specific Conditions Not Correct",
    0x95:"Reserved For Specific Conditions Not Correct",
    0x96:"Reserved For Specific Conditions Not Correct",
    0x97:"Reserved For Specific Conditions Not Correct",
    0x98:"Reserved For Specific Conditions Not Correct",
    0x99:"Reserved For Specific Conditions Not Correct",
    0x9a:"Reserved For Specific Conditions Not Correct",
    0x9b:"Reserved For Specific Conditions Not Correct",
    0x9c:"Reserved For Specific Conditions Not Correct",
    0x9d:"Reserved For Specific Conditions Not Correct",
    0x9e:"Reserved For Specific Conditions Not Correct",
    0x9f:"Reserved For Specific Conditions Not Correct",
    0xa0:"Reserved For Specific Conditions Not Correct",
    0xa1:"Reserved For Specific Conditions Not Correct",
    0xa2:"Reserved For Specific Conditions Not Correct",
    0xa3:"Reserved For Specific Conditions Not Correct",
    0xa4:"Reserved For Specific Conditions Not Correct",
    0xa5:"Reserved For Specific Conditions Not Correct",
    0xa6:"Reserved For Specific Conditions Not Correct",
    0xa7:"Reserved For Specific Conditions Not Correct",
    0xa8:"Reserved For Specific Conditions Not Correct",
    0xa9:"Reserved For Specific Conditions Not Correct",
    0xaa:"Reserved For Specific Conditions Not Correct",
    0xab:"Reserved For Specific Conditions Not Correct",
    0xac:"Reserved For Specific Conditions Not Correct",
    0xad:"Reserved For Specific Conditions Not Correct",
    0xae:"Reserved For Specific Conditions Not Correct",
    0xaf:"Reserved For Specific Conditions Not Correct",
    0xb0:"Reserved For Specific Conditions Not Correct",
    0xb1:"Reserved For Specific Conditions Not Correct",
    0xb2:"Reserved For Specific Conditions Not Correct",
    0xb3:"Reserved For Specific Conditions Not Correct",
    0xb4:"Reserved For Specific Conditions Not Correct",
    0xb5:"Reserved For Specific Conditions Not Correct",
    0xb6:"Reserved For Specific Conditions Not Correct",
    0xb7:"Reserved For Specific Conditions Not Correct",
    0xb8:"Reserved For Specific Conditions Not Correct",
    0xb9:"Reserved For Specific Conditions Not Correct",
    0xba:"Reserved For Specific Conditions Not Correct",
    0xbb:"Reserved For Specific Conditions Not Correct",
    0xbc:"Reserved For Specific Conditions Not Correct",
    0xbd:"Reserved For Specific Conditions Not Correct",
    0xbe:"Reserved For Specific Conditions Not Correct",
    0xbf:"Reserved For Specific Conditions Not Correct",
    0xc0:"Reserved For Specific Conditions Not Correct",
    0xc1:"Reserved For Specific Conditions Not Correct",
    0xc2:"Reserved For Specific Conditions Not Correct",
    0xc3:"Reserved For Specific Conditions Not Correct",
    0xc4:"Reserved For Specific Conditions Not Correct",
    0xc5:"Reserved For Specific Conditions Not Correct",
    0xc6:"Reserved For Specific Conditions Not Correct",
    0xc7:"Reserved For Specific Conditions Not Correct",
    0xc8:"Reserved For Specific Conditions Not Correct",
    0xc9:"Reserved For Specific Conditions Not Correct",
    0xca:"Reserved For Specific Conditions Not Correct",
    0xcb:"Reserved For Specific Conditions Not Correct",
    0xcc:"Reserved For Specific Conditions Not Correct",
    0xcd:"Reserved For Specific Conditions Not Correct",
    0xce:"Reserved For Specific Conditions Not Correct",
    0xcf:"Reserved For Specific Conditions Not Correct",
    0xd0:"Reserved For Specific Conditions Not Correct",
    0xd1:"Reserved For Specific Conditions Not Correct",
    0xd2:"Reserved For Specific Conditions Not Correct",
    0xd3:"Reserved For Specific Conditions Not Correct",
    0xd4:"Reserved For Specific Conditions Not Correct",
    0xd5:"Reserved For Specific Conditions Not Correct",
    0xd6:"Reserved For Specific Conditions Not Correct",
    0xd7:"Reserved For Specific Conditions Not Correct",
    0xd8:"Reserved For Specific Conditions Not Correct",
    0xd9:"Reserved For Specific Conditions Not Correct",
    0xda:"Reserved For Specific Conditions Not Correct",
    0xdb:"Reserved For Specific Conditions Not Correct",
    0xdc:"Reserved For Specific Conditions Not Correct",
    0xdd:"Reserved For Specific Conditions Not Correct",
    0xde:"Reserved For Specific Conditions Not Correct",
    0xdf:"Reserved For Specific Conditions Not Correct",
    0xe0:"Reserved For Specific Conditions Not Correct",
    0xe1:"Reserved For Specific Conditions Not Correct",
    0xe2:"Reserved For Specific Conditions Not Correct",
    0xe3:"Reserved For Specific Conditions Not Correct",
    0xe4:"Reserved For Specific Conditions Not Correct",
    0xe5:"Reserved For Specific Conditions Not Correct",
    0xe6:"Reserved For Specific Conditions Not Correct",
    0xe7:"Reserved For Specific Conditions Not Correct",
    0xe8:"Reserved For Specific Conditions Not Correct",
    0xe9:"Reserved For Specific Conditions Not Correct",
    0xea:"Reserved For Specific Conditions Not Correct",
    0xeb:"Reserved For Specific Conditions Not Correct",
    0xec:"Reserved For Specific Conditions Not Correct",
    0xed:"Reserved For Specific Conditions Not Correct",
    0xee:"Reserved For Specific Conditions Not Correct",
    0xef:"Reserved For Specific Conditions Not Correct",
    0xf0:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xf1:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xf2:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xf3:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xf4:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xf5:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xf6:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xf7:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xf8:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xf9:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xfa:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xfb:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xfc:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xfd:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xfe:"Vehicle Manufacturer Specific Conditions Not Correct",
    0xff:"ISO SAE Reserved"
}

UDS_SERVICE_NAMES = {
    0x10: "DIAGNOSTIC_SESSION_CONTROL",
    0x11: "ECU_RESET",
    0x14: "CLEAR_DIAGNOSTIC_INFORMATION",
    0x19: "READ_DTC_INFORMATION",
    0x20: "RETURN_TO_NORMAL",
    0x22: "READ_DATA_BY_IDENTIFIER",
    0x23: "READ_MEMORY_BY_ADDRESS",
    0x24: "READ_SCALING_DATA_BY_IDENTIFIER",
    0x27: "SECURITY_ACCESS",
    0x28: "COMMUNICATION_CONTROL",
    0x29: "AUTHENTICATION",
    0x2A: "READ_DATA_BY_PERIODIC_IDENTIFIER",
    0x2C: "DYNAMICALLY_DEFINE_DATA_IDENTIFIER",
    0x2D: "DEFINE_PID_BY_MEMORY_ADDRESS",
    0x2E: "WRITE_DATA_BY_IDENTIFIER",
    0x2F: "INPUT_OUTPUT_CONTROL_BY_IDENTIFIER",
    0x31: "ROUTINE_CONTROL",
    0x34: "REQUEST_DOWNLOAD",
    0x35: "REQUEST_UPLOAD",
    0x36: "TRANSFER_DATA",
    0x37: "REQUEST_TRANSFER_EXIT",
    0x38: "REQUEST_FILE_TRANSFER",
    0x3D: "WRITE_MEMORY_BY_ADDRESS",
    0x3E: "TESTER_PRESENT",
    0x7F: "NEGATIVE_RESPONSE",
    0x83: "ACCESS_TIMING_PARAMETER",
    0x84: "SECURED_DATA_TRANSMISSION",
    0x85: "CONTROL_DTC_SETTING",
    0x86: "RESPONSE_ON_EVENT",
    0x87: "LINK_CONTROL"

}

RED = "\033[1;31m"
BLUE = "\033[1;34m"
CYAN = "\033[1;36m"
WHITE = "\033[1;37m"
YELLOW = "\033[1;33m"
GREEN = "\033[1;32m"
RESET = "\033[1;0m"
BOLD = "\033[;1m"
REVERSE = "\033[;7m"


logging.basicConfig(filename='catl_can.log', level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def dec_to_hex(n, id_flag=False,digits=3):
    if n > 0x7ff and not id_flag:
        digits = 8
    hex_str = hex(n)[2:]
    zeros = digits - len(hex_str)
    if zeros > 0:
        hex_str = '0' * zeros + hex_str
    return '0x' + hex_str

def getData(socket, filename):
    with open(filename) as f:
        content = json.load(f)
        for each in content:
            if int(each[0],16) == socket.tx_id and int(each[1],16) == socket.rx_id:
                return each[2]
    return None

def print_data(send,canid, data,show_data=True):
    if type(data) == UDS:
        data = binascii.b2a_hex(raw(data)).decode('utf-8')

    elif type(data) is not str:
        data = binascii.b2a_hex(data).decode('utf-8')

    data = splitWithEmpty(data)
    if send:
        if show_data:
            print('TX【'+canid+'】: --> '+data)
        logger.info('TX【'+canid+'】: --> '+data)
    else:
        if show_data:
            print('RX【'+canid+'】: <-- '+data)
        logger.info('RX【'+canid+'】: <-- '+data)
        
def genRandom(length):
    random_bytes = secrets.token_bytes(length)
    hex_string = secrets.token_hex(length)
    return hex_string

def stop_sniff(packet):
    pass  # 捕获到一个数据包后停止
    
def splitWithEmpty(data):
    if type(data) == bytes:
        data = binascii.b2a_hex(data).decode('utf-8')
    elif type(data) == UDS:
        data = raw(data)
        data = binascii.b2a_hex(data).decode('utf-8')
    data = data.upper()
    printdata = ' '.join(data[i:i+2] for i in range(0, len(data), 2))
    return printdata

def Bitmask(cid, sid):
    if cid > 0x7ff or sid > 0x7ff:
        return cid ^ sid ^ 0x1fffffff
    else:
        return cid ^ sid ^ 0x7ff

def deal_scan_data(socket, scan_range, num):
    for each in scan_range:
        resp = Sender_Receiver(socket, each, verbos=False,dump=True,show_data=False)
        if resp == 'delay_error':
            return 'delay_error'
        if resp is None:
            continue
        if resp and (num & 0b100 == 0b100) and ((resp[UDS].service !=  0x7f) or resp[UDS_NR].negativeResponseCode != 0x11):
            if int(each[:2],16) in UDS_SERVICE_NAMES.keys():
                print('0x'+each[:2],' ', UDS_SERVICE_NAMES[int(each[:2],16)])
            else:
                print('0x'+each[:2],' ', "Unknown")
        elif resp and (resp[UDS].service != 0x7f) or (((num & 0b010 == 0b010) or (num & 0b001 == 0b001)) and resp[UDS_NR].negativeResponseCode != 0x12):
            print(' '.join(each[i:i+2] for i in range(0, len(each), 2)))
    return True

def scan_func(socketPool,num):
    for socket in socketPool:
        res = True

        if num & 0b100 == 0b100:
            logger.info('[*] Scanning for Support Services......')
            print('[+] Support Services: ')
            scan_range = (x for x in range(0x100) if not x & 0x40)
            # scan_range = (x for x in range(0x100))
            senddata = (hex(each)[2:].rjust(2,'0') + '00' for each in scan_range)

            res = deal_scan_data(socket, senddata, 0b100)
            logger.info('--------------END--------------')
            if res == 'delay_error':
                return False
            elif res is False:
                return res

        if num & 0b001 == 0b001:
            logger.info('[*] Scanning for Support Security Level......')
            print('\n',end='')
            print('[+] Security Levels: ')
            scan_range = range(1, 256, 2)
            senddata = ('27' + hex(each)[2:].rjust(2,'0') for each in scan_range)
            res = deal_scan_data(socket, senddata, 0b001)
            logger.info('--------------END--------------')
            if res == 'delay_error':
                return False
            elif res is False:
                return res

        if num & 0b010 == 0b010:
            session_range = [x for x in range(0xff, 0, -1)]
            senddata = ('10' + hex(each)[2:].rjust(2,'0') for each in session_range)
            logger.info('[*] Scanning for Support Sessions......')
            print('\n[+] Support Sessions: ')
            res = deal_scan_data(socket, senddata, 0b010)     
            logger.info('--------------END--------------')
            if res == 'delay_error':
                return False
            elif res is False:
                return res
        return res

def Sender_Receiver(socket,each,verbos=True,dump=False,show_data=True):
    if type(each) is str:
        sendData = binascii.a2b_hex(each)
    else:
        sendData = each

    isoTpMessage = ISOTP(sendData)
    uds_data = None
    print_data(1,dec_to_hex(socket.tx_id),each,show_data)
    try:
        pkg = socket.sr1(isoTpMessage,timeout=global_p2_timeout,verbose=0)
    except TimeoutError as e:
        pkg = None

    if pkg:
        uds_data = UDS(pkg.data)
        print_data(0,dec_to_hex(socket.rx_id),uds_data,show_data)
        

        while uds_data is not None and ((uds_data[UDS].service == 0x7f and uds_data[UDS_NR].negativeResponseCode == 0x78)):
            startTime = time.time()
            
            while True:
                pkg = socket.sniff(timeout=1.5, prn=stop_sniff, count=1)
                if len(pkg.res) != 0:
                    uds_data = UDS(pkg.res[0].data)
                    print_data(0, dec_to_hex(socket.rx_id), uds_data,show_data)
                    
                    if uds_data[UDS].service == 0x7f and uds_data[UDS_NR].negativeResponseCode == 0x78:
                        startTime = time.time()  # 重新获取startTime
                    else:
                        break

                elif len(pkg.res) == 0 and time.time() - startTime <= global_p2_star_time:
                    socket.send(raw(UDS(binascii.a2b_hex('3e80'))))
                    print_data(1, dec_to_hex(socket.tx_id), '3E80',show_data)
                    continue

                elif time.time() - startTime > global_p2_star_time:
                    uds_data = None
                    break

        while uds_data is not None and ((uds_data[UDS].service == 0x7f and uds_data[UDS_NR].requestServiceId != UDS(sendData).service) or (uds_data[UDS].service != 0x7f and uds_data[UDS].service != UDS(sendData).service + 0x40) or (uds_data.service == 0x62 and uds_data.dataIdentifier != UDS(sendData).identifiers[0]) or (uds_data.service == 0x6f and uds_data.dataIdentifier != UDS(sendData).dataIdentifier) or (uds_data.service == 0x6e and uds_data.dataIdentifier != UDS(sendData).dataIdentifier) or (uds_data.service == 0x71 and uds_data.routineIdentifier != UDS(sendData).routineIdentifier)):           
            pkg = socket.sniff(timeout=global_p2_star_time,prn=stop_sniff, count=1)
            if len(pkg.res) != 0:
                uds_data = UDS(pkg.res[0].data)
                print_data(0,dec_to_hex(socket.rx_id),uds_data,show_data)
            else:
                uds_data = None
                break
    if dump:
        return uds_data

    if uds_data:
        if uds_data[UDS].service == 0x7f:
            
            if uds_data[UDS_NR].requestServiceId == 0x22 and verbos:
                print('0x'+binascii.b2a_hex(sendData[1:]).decode('utf-8'),'\t',negativeResponseCodes[uds_data[UDS_NR].negativeResponseCode])
            
            elif uds_data[UDS_NR].requestServiceId == 0x2e and verbos:
                print('0x'+binascii.b2a_hex(sendData[1:3]).decode('utf-8'),'\t',negativeResponseCodes[uds_data[UDS_NR].negativeResponseCode])
            
            elif verbos:
                if len(each)>36:
                    print(binascii.b2a_hex(sendData[:18]).decode('utf-8'),'......',binascii.b2a_hex(sendData[-10:]).decode('utf-8'),' ',negativeResponseCodes[uds_data[UDS_NR].negativeResponseCode])
                else:
                    print(binascii.b2a_hex(sendData).decode('utf-8'),' ',negativeResponseCodes[uds_data[UDS_NR].negativeResponseCode])
        else:
            if uds_data[UDS].service ==0x67 and uds_data[UDS].securityAccessType % 2 == 0:
                print('[+] 0x'+dec_to_hex(socket.tx_id)+' Bypass 27...')
                
            elif uds_data[UDS].service == 0x6e:
                if type(each) is not str:
                    each = binascii.b2a_hex(each).decode('utf-8')
                print('[+]','CANID:',dec_to_hex(socket.tx_id),'\tDID',each[2:6],'\t','Success')

    return uds_data


def enter_Mode(socket):
    while True:
        uds_data = Sender_Receiver(isoTpSocket,binascii.a2b_hex('3101F518'),show_data=False,verbos=False)
        if uds_data and uds_data[UDS].service == 0x71:
            break

tx = 0x18E06188
rx = 0x18E06180
isoTpSocket = ISOTPNativeSocket('can0',tx_id=tx,rx_id=rx,fd=True,padding=True,basecls=ISOTP)

# enter_Mode(isoTpSocket)

# 控制会话
# sessionData = ['1003']
# for each in sessionData:
#     Sender_Receiver(isoTpSocket,binascii.a2b_hex(each),verbos=False,show_data=False)




#测试2C

def pt_2c_01(socket):
    ss = ['f2','f3']

    for each in ss:
        for i in range(0,0x100):
            sendData = '2C01'+each+hex(i)[2:].rjust(2,'0')
            resp = Sender_Receiver(socket,binascii.a2b_hex(sendData),verbos=False,show_data=False)
            if resp and resp[UDS].service == 0x7f:
                print('Send:',sendData,'\tResponse:',negativeResponseCodes[resp[UDS_NR].negativeResponseCode])

            elif resp and resp[UDS].service == 0x6c:
                print(binascii.b2a_hex(resp).decode('utf-8'))

def pt_2c_02(socket):
    for i in range(100):
        memoryAddress = genRandom(4)
        sendData = '2C02F30114'+memoryAddress+'04'
        resp = Sender_Receiver(socket,binascii.a2b_hex(sendData),verbos=False,show_data=False)
        if resp and resp[UDS].service == 0x7f:
            print('Send:',sendData,'\tResponse:',negativeResponseCodes[resp[UDS_NR].negativeResponseCode])

        elif resp and resp[UDS].service == 0x6c:
            print(binascii.b2a_hex(resp).decode('utf-8'))


# pt_2c_01(isoTpSocket)
# pt_2c_02(isoTpSocket)


# 测试2f服务

def pt_2f(socket):
    dids = []
    iocp = [0,1,2]
    sendCmdata = ['00','01','10','11']
    diddata = getData(isoTpSocket,'./catldid/1001_dids.json')
    for k, v in diddata.items():
        dids.append(int(k[2:],16))
    output_list = [UDS() / UDS_IOCBI(dataIdentifier=x_did,controlOptionRecord=0x03,controlEnableMaskRecord=binascii.a2b_hex(x_record)) for x_record,x_did in itertools.product(sendCmdata,dids)]
    sendData = [UDS() / UDS_IOCBI(dataIdentifier=x_did,controlOptionRecord=x_record) for x_record,x_did in itertools.product(iocp,dids)]
    sendData.extend(output_list)
    for each in sendData:
        resp = Sender_Receiver(socket,raw(each),verbos=False,show_data=False)
        if resp and resp[UDS].service == 0x7f:
            print('Send:',binascii.b2a_hex(raw(each)).decode('utf-8'),'\tResponse:',negativeResponseCodes[resp[UDS_NR].negativeResponseCode])
        
        elif resp and resp[UDS].service == 0x6f:
            print('IOCBI success',binascii.b2a_hex(each).decode('utf-8'))
# pt_2f(isoTpSocket)


#测试3D服务
def pt_3d(socket):
    address_range = [[0x90000000,0x9fffffff],
                 [0xa0000000,0xa02fffff],
                 [0xa0300000,0xa05fffff],
                 [0xa0600000,0xa08fffff],
                 [0xa0900000,0xa0bfffff],
                 [0xa0c00000,0xa0efffff],
                 [0xa0f00000,0xa0ffffff],
                 [0xa1000000,0xaf3fffff],
                 [0xaf400000,0xaf400dff],
                 [0xaf400e00,0xaf400fff],
                 [0xaf401000,0xaf405fff],
                 [0xaf406000,0xafbfffff],
                 [0xafc00000,0xafc1ffff],
                 [0xafc20000,0xafffffff],
                 [0xb0000000,0xbfffffff],
                 [0xc0000000,0xcfffffff],
                 [0xd0000000,0xdfffffff]]
    
    samples_per_range = 100  # 每个范围采样100个地址
    for start, end in address_range:
        # 确保起始和结束地址被采样到
        addresses = [start, end-256]
        
        # 计算剩余需要采样的数量
        remaining_samples = samples_per_range - 2
        
        # 在范围内随机生成剩余的地址
        range_size = end - start
        if remaining_samples > 0:
            step = range_size // (remaining_samples + 1)  # 计算采样间隔
            for i in range(remaining_samples):
                # 生成一个随机偏移量，确保不会与已有地址重复
                offset = random.randint(1, step - 1)
                addr = start + (i + 1) * step + offset
                addr = min(addr, end)  # 确保不超过范围
                addresses.append(addr)
        if remaining_samples == 0:
            time.sleep(60)
        print(hex(start),'-',hex(end))
        # 对每个采样的地址进行测试
        for addr in addresses:
            memoryAddress = hex(addr)[2:].zfill(8)  # 转换为8位16进制字符串

            sendData = '3D14'+memoryAddress+'0411223344'
            resp = Sender_Receiver(socket,binascii.a2b_hex(sendData),verbos=False,show_data=False)
            if resp and resp[UDS].service == 0x7f:
                print('Send:',sendData,'\tResponse:',negativeResponseCodes[resp[UDS_NR].negativeResponseCode])

            elif resp and resp[UDS].service == 0x7d:
                print('Success:',sendData,'\tResponse:',binascii.b2a_hex(resp[UDS_RMBAPR].dataRecord).decode('utf-8'))
pt_3d(isoTpSocket)

# 29服务测试
def pt_29_subservice(socket):
    for i in range(0x100):
        sendData = '29'+hex(i)[2:].rjust(2,'0')
        resp = Sender_Receiver(socket,binascii.a2b_hex(sendData),verbos=False,show_data=False)
        if resp and resp[UDS].service == 0x7f:
            if resp[UDS_NR].negativeResponseCode == 0x12:
                continue
            print('Send:',sendData,'\tResponse:',negativeResponseCodes[resp[UDS_NR].negativeResponseCode])
        elif resp and resp[UDS].service == 0x69:
            print('Send:',sendData,'\tPositive Response')
# pt_29_subservice(isoTpSocket)

senddata = ['290100fd0000',
            '290200fdfd',
            '2903fd00',
            '2904fdfd',
            '2900',
            '2908']
senddata = ['29010003bb2d2d2d2d2d424547494e2043455254494649434154452d2d2d2d2d0a4d4949436c6a434341687967417749424167495543793448546248724b78502b784a3544416a74635034583750706377436759494b6f5a497a6a304541774d770a67594578437a414a42674e5642415954416b4e4f4d524577447759445651514944416854614746755a326868615445524d4138474131554542777749633268680a626d646f59576b78446a414d42674e5642416f4d4258526c633351794d513077437759445651514c444152305a584e304d5130774377594456515144444152300a5a584e304d5234774841594a4b6f5a496876634e41516b42466738324e4455334d6a597a4f4542786353356a623230774868634e4d6a51774e5449794d44557a0a4d544d335768634e4d7a51774e5449774d44557a4d544d33576a43426754454c4d416b474131554542684d43513034784554415042674e564241674d43464e6f0a5957356e614746704d52457744775944565151484441687a614746755a3268686154454f4d41774741315545436777466447567a644449784454414c42674e560a4241734d4248526c633351784454414c42674e5642414d4d4248526c63335178486a416342676b71686b69473977304243514557447a59304e5463794e6a4d340a514846784c6d4e76625442324d42414742797147534d3439416745474253754242414169413249414247474150334d4f4c4e38666d396b62697256327043766d0a742f55384d463737717159794b68626f307753346f4f734c72736b72514473727a7558615a79704c66765175796861645666564b56514d6b766241397163564e0a71612b596a7759487341344d6b364659312f742f767438652f706d34483436353444344e5444733078614e544d464577485159445652304f424259454647377a0a4749667359313871526e306668743075464f374b4d4768564d42384741315564497751594d4261414647377a4749667359313871526e306668743075464f374b0a4d4768564d41384741315564457745422f7751464d414d4241663877436759494b6f5a497a6a304541774d44614141775a514978414e574c315643614d64674a0a77656a6f6b5277537569386558734559564c73714a4f75366864734d35536279583734334c746d2b4844724c49624542645a32466d6749774861344565472b6c0a3576686c70496835537069636e7a394c324e364b672f6a565876683148336d6a474f35436c6f704543494f305950734e6970506f4c4270410a2d2d2d2d2d454e442043455254494649434154452d2d2d2d2d',
            '29020003bb2d2d2d2d2d424547494e2043455254494649434154452d2d2d2d2d0a4d4949436c6a434341687967417749424167495543793448546248724b78502b784a3544416a74635034583750706377436759494b6f5a497a6a304541774d770a67594578437a414a42674e5642415954416b4e4f4d524577447759445651514944416854614746755a326868615445524d4138474131554542777749633268680a626d646f59576b78446a414d42674e5642416f4d4258526c633351794d513077437759445651514c444152305a584e304d5130774377594456515144444152300a5a584e304d5234774841594a4b6f5a496876634e41516b42466738324e4455334d6a597a4f4542786353356a623230774868634e4d6a51774e5449794d44557a0a4d544d335768634e4d7a51774e5449774d44557a4d544d33576a43426754454c4d416b474131554542684d43513034784554415042674e564241674d43464e6f0a5957356e614746704d52457744775944565151484441687a614746755a3268686154454f4d41774741315545436777466447567a644449784454414c42674e560a4241734d4248526c633351784454414c42674e5642414d4d4248526c63335178486a416342676b71686b69473977304243514557447a59304e5463794e6a4d340a514846784c6d4e76625442324d42414742797147534d3439416745474253754242414169413249414247474150334d4f4c4e38666d396b62697256327043766d0a742f55384d463737717159794b68626f307753346f4f734c72736b72514473727a7558615a79704c66765175796861645666564b56514d6b766241397163564e0a71612b596a7759487341344d6b364659312f742f767438652f706d34483436353444344e5444733078614e544d464577485159445652304f424259454647377a0a4749667359313871526e306668743075464f374b4d4768564d42384741315564497751594d4261414647377a4749667359313871526e306668743075464f374b0a4d4768564d41384741315564457745422f7751464d414d4241663877436759494b6f5a497a6a304541774d44614141775a514978414e574c315643614d64674a0a77656a6f6b5277537569386558734559564c73714a4f75366864734d35536279583734334c746d2b4844724c49624542645a32466d6749774861344565472b6c0a3576686c70496835537069636e7a394c324e364b672f6a565876683148336d6a474f35436c6f704543494f305950734e6970506f4c4270410a2d2d2d2d2d454e442043455254494649434154452d2d2d2d2d',
            '2904fdfd03bb29020003bb2d2d2d2d2d424547494e2043455254494649434154452d2d2d2d2d0a4d4949436c6a434341687967417749424167495543793448546248724b78502b784a3544416a74635034583750706377436759494b6f5a497a6a304541774d770a67594578437a414a42674e5642415954416b4e4f4d524577447759445651514944416854614746755a326868615445524d4138474131554542777749633268680a626d646f59576b78446a414d42674e5642416f4d4258526c633351794d513077437759445651514c444152305a584e304d5130774377594456515144444152300a5a584e304d5234774841594a4b6f5a496876634e41516b42466738324e4455334d6a597a4f4542786353356a623230774868634e4d6a51774e5449794d44557a0a4d544d335768634e4d7a51774e5449774d44557a4d544d33576a43426754454c4d416b474131554542684d43513034784554415042674e564241674d43464e6f0a5957356e614746704d52457744775944565151484441687a614746755a3268686154454f4d41774741315545436777466447567a644449784454414c42674e560a4241734d4248526c633351784454414c42674e5642414d4d4248526c63335178486a416342676b71686b69473977304243514557447a59304e5463794e6a4d340a514846784c6d4e76625442324d42414742797147534d3439416745474253754242414169413249414247474150334d4f4c4e38666d396b62697256327043766d0a742f55384d463737717159794b68626f307753346f4f734c72736b72514473727a7558615a79704c66765175796861645666564b56514d6b766241397163564e0a71612b596a7759487341344d6b364659312f742f767438652f706d34483436353444344e5444733078614e544d464577485159445652304f424259454647377a0a4749667359313871526e306668743075464f374b4d4768564d42384741315564497751594d4261414647377a4749667359313871526e306668743075464f374b0a4d4768564d41384741315564457745422f7751464d414d4241663877436759494b6f5a497a6a304541774d44614141775a514978414e574c315643614d64674a0a77656a6f6b5277537569386558734559564c73714a4f75366864734d35536279583734334c746d2b4844724c49624542645a32466d6749774861344565472b6c0a3576686c70496835537069636e7a394c324e364b672f6a565876683148336d6a474f35436c6f704543494f305950734e6970506f4c4270410a2d2d2d2d2d454e442043455254494649434154452d2d2d2d2d'
            ]
# for each in senddata:
#     resp = Sender_Receiver(isoTpSocket,binascii.a2b_hex(each),verbos=False,show_data=True)


def pt23(socket, memorySizeLen, memoryAddressLen, memorySize):
    success = []
    memory_map = set()  # 记录已读取的内存地址
    total_space = 0xFFFFFFFF  # 32位系统的地址空间是4GB (0x00000000-0xFFFFFFFF)
    start_time = time.time()  # 记录开始时间
    address_range = [[0x90000000,0x9fffffff],
                 [0xa0000000,0xa02fffff],
                 [0xa0300000,0xa05fffff],
                 [0xa0600000,0xa08fffff],
                 [0xa0900000,0xa0bfffff],
                 [0xa0c00000,0xa0efffff],
                 [0xa0f00000,0xa0ffffff],
                 [0xa1000000,0xaf3fffff],
                 [0xaf400000,0xaf400dff],
                 [0xaf400e00,0xaf400fff],
                 [0xaf401000,0xaf405fff],
                 [0xaf406000,0xafbfffff],
                 [0xafc00000,0xafc1ffff],
                 [0xafc20000,0xafffffff],
                 [0xb0000000,0xbfffffff],
                 [0xc0000000,0xcfffffff],
                 [0xd0000000,0xdfffffff]]
    
    samples_per_range = 1000  # 每个范围采样100个地址
    
    try:
        for start, end in address_range:
            # 确保起始和结束地址被采样到
            addresses = [start, end-256]
            
            # 计算剩余需要采样的数量
            remaining_samples = samples_per_range - 2
            
            # 在范围内随机生成剩余的地址
            range_size = end - start
            if remaining_samples > 0:
                step = range_size // (remaining_samples + 1)  # 计算采样间隔
                for i in range(remaining_samples):
                    # 生成一个随机偏移量，确保不会与已有地址重复
                    offset = random.randint(1, step - 1)
                    addr = start + (i + 1) * step + offset
                    addr = min(addr, end)  # 确保不超过范围
                    addresses.append(addr)
            if remaining_samples == 0:
                time.sleep(60)
            print(hex(start),'-',hex(end))
            # 对每个采样的地址进行测试
            for addr in addresses:
                memoryAddress = hex(addr)[2:].zfill(8)  # 转换为8位16进制字符串
                sendData = '23' + memorySizeLen + memoryAddressLen + memoryAddress + memorySize
                resp = Sender_Receiver(socket, binascii.a2b_hex(sendData), verbos=False, show_data=False)

                # 记录所有尝试读取的地址范围
                start_addr = addr
                size = int(memorySize, 16)
                for test_addr in range(start_addr, start_addr + size):
                    memory_map.add(test_addr & 0xFFFFFFFF)  # 确保地址在32位范围内

                if resp and resp[UDS].service == 0x7f:
                    print('Address:', memoryAddress, '\tSize:', '256B', '\tResponse:', negativeResponseCodes[resp[UDS_NR].negativeResponseCode])

                elif resp and resp[UDS].service == 0x63:
                    print('Address:', memoryAddress, '\tSize:', '256B', '\tResponse:', binascii.b2a_hex(resp[UDS_RMBAPR].dataRecord).decode('utf-8'))
                    success.append(memoryAddress)

    except KeyboardInterrupt:
        pass
    finally:
        # 计算运行时间
        run_time = time.time() - start_time
        hours = int(run_time // 3600)
        minutes = int((run_time % 3600) // 60)
        seconds = int(run_time % 60) 
        with open('catl_memory_map.pkl', 'wb') as pkl_file: 
            pickle.dump(memory_map, pkl_file)
        # 计算覆盖率
        coverage = (len(memory_map) / total_space) * 100
        print("\n--- Memory Read Statistics ---")
        print(f"Total Address Space: 4GB (0x00000000-0xFFFFFFFF)")
        print(f"Coverage: {coverage:.8f}%")  # 增加精度，因为覆盖率会很小
        print(f"Run time: {hours:02d}:{minutes:02d}:{seconds:02d}")  # 显示为 HH:MM:SS 格式
        return success, memory_map, coverage

# 测试23服务
# success, memory_map, coverage = pt23(isoTpSocket,'2', '4', '0100')



# print(success)





# 单条数据发送
# Sender_Receiver(isoTpSocket,binascii.a2b_hex('1003'),show_data=True)

# 测试27
# uds_data = Sender_Receiver(isoTpSocket,binascii.a2b_hex('2711'),show_data=True)
# if uds_data and uds_data[UDS].service == 0x67:
#     seed = binascii.b2a_hex(uds_data[UDS_SAPR].securitySeed).decode('utf-8')
#     print(seed)
#     uds_data = Sender_Receiver(isoTpSocket,binascii.a2b_hex('2712'+'0'*128),show_data=True)
#     uds_data = Sender_Receiver(isoTpSocket,binascii.a2b_hex('2712'+'1'*128),show_data=True)
#     uds_data = Sender_Receiver(isoTpSocket,binascii.a2b_hex('2712'+'2'*128),show_data=True)
#     uds_data = Sender_Receiver(isoTpSocket,binascii.a2b_hex('2712'+'3'*128),show_data=True)
#     uds_data = Sender_Receiver(isoTpSocket,binascii.a2b_hex('2712'+'4'*128),show_data=True)
#     uds_data = Sender_Receiver(isoTpSocket,binascii.a2b_hex('2712'+'5'*128),show_data=True)

#扫描服务
# flag = 0b100 # 服务
# flag = 0b010 # 会话
# flag = 0b001 # 安全

# res = scan_func([isoTpSocket],flag)

#dumpdid
# verboseMode = False
# dataMode = True

# output_list = [x for x in range(0x1,0x10000)]
# content = []
# with open('./catldid/1002_dids.json','w') as f:
#     json.dump(content, f)

# length = len(output_list)
# formatlen = str(len(str(length)))
# suffixstr = '%(index)'+formatlen+'d/%(max)'+formatlen+'d'
# s_id = dec_to_hex(isoTpSocket.tx_id)
# t_id = dec_to_hex(isoTpSocket.rx_id)

# tmp_dids = {}
# bar = Bar(s_id+' '+t_id, max=length,suffix=suffixstr)

# for each in output_list:

#     data =raw(UDS() / UDS_RDBI(identifiers=[each]))
#     uds_data = None

#     if verboseMode is False and dataMode is False:
#         uds_data = Sender_Receiver(isoTpSocket,data,dump=True,show_data=False)
#         bar.next()

#     elif verboseMode or dataMode:
#         uds_data = Sender_Receiver(isoTpSocket,data,verbos=verboseMode,show_data=False)

#     if uds_data and uds_data[UDS].service == 0x7f and uds_data[UDS_NR].negativeResponseCode == 0x11:
#         break

#     elif uds_data and uds_data[UDS].service == 0x62:
#         if uds_data.haslayer('Raw'):
#             rcv_data =  binascii.b2a_hex(uds_data[Raw].load).decode('utf-8')
#             if dataMode or verboseMode:
#                 print(dec_to_hex(each,id_flag=True,digits=4),'\t',rcv_data)
#             tmp_dids[dec_to_hex(uds_data[UDS].dataIdentifier,id_flag=True,digits=4)] = rcv_data

# content.append([s_id,t_id,tmp_dids])
# with open('./catldid/1002_dids.json','w') as f_new:
#     json.dump(content, f_new)
# bar.finish()
# print('All DIDs are Saved in the 1002_dids.json file')


#2e写DID

# def pt2e(socket, diddata):
#     for k, v in diddata.items():
#         senddata = UDS() / UDS_WDBI(dataIdentifier = int(k,16)) / Raw(load=binascii.a2b_hex(v))
#         resp = Sender_Receiver(socket,raw(senddata),show_data=False)

# dids = []
# diddata = getData(isoTpSocket,'./catldid/1083_dids.json')
# kk = pt2e(isoTpSocket, diddata)