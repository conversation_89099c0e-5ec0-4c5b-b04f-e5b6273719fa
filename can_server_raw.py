#!/usr/bin/env python3
"""
CAN Server Program (Raw Mode)
Listens on CAN0 interface and responds to specific messages using raw CAN frames.
"""

import os
import signal
import random
import binascii
import sys
import time
import threading
from scapy.layers.can import CAN
from scapy.all import conf, raw, Packet

# Define colors for terminal output
RED = "\033[1;31m"
GREEN = "\033[1;32m"
YELLOW = "\033[1;33m"
BLUE = "\033[1;34m"
CYAN = "\033[1;36m"
RESET = "\033[0m"

# Define CAN IDs
REQUEST_ID = 0x618
RESPONSE_ID = 0x718

# Flag to control the main loop
running = True
stop_event = threading.Event()

def signal_handler(sig, frame):
    """Handle Ctrl+C to gracefully exit the program"""
    global running
    print(f"\n{YELLOW}[*] Stopping CAN server...{RESET}")
    running = False
    stop_event.set()

def setup_can_socket():
    """Setup and check CAN interface and create socket"""
    try:
        # Import here to avoid circular imports
        from scapy.contrib.cansocket import CANSocket
        
        # Create a raw CAN socket
        can_socket = CANSocket(channel='can0')
        print(f"{GREEN}[+] Successfully connected to CAN0 interface{RESET}")
        return can_socket
    except Exception as e:
        print(f"{RED}[!] Error setting up CAN socket: {e}{RESET}")
        print(f"{YELLOW}[*] Try setting up the interface with:{RESET}")
        print("    sudo ip link set can0 up type can bitrate 500000")
        sys.exit(1)

def generate_random_bytes(length=4):
    """Generate random bytes for the response"""
    return bytes([random.randint(0, 255) for _ in range(length)])

def format_bytes(data):
    """Format bytes for display"""
    if isinstance(data, bytes):
        return ' '.join([f"{b:02X}" for b in data])
    elif isinstance(data, Packet):
        return ' '.join([f"{b:02X}" for b in raw(data)])
    else:
        return str(data)

def main():
    """Main function to run the CAN server"""
    print(f"{CYAN}=== CAN Server Program (Raw Mode) ==={RESET}")
    print(f"{CYAN}Listening on CAN0 interface{RESET}")
    print(f"{CYAN}Monitoring for ID: 0x{REQUEST_ID:03X}{RESET}")
    print(f"{CYAN}Responding with ID: 0x{RESPONSE_ID:03X}{RESET}")
    print(f"{YELLOW}Press Ctrl+C to exit{RESET}")
    
    # Register signal handler for Ctrl+C
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Setup CAN socket
    can_socket = setup_can_socket()
    
    # Main loop
    while running and not stop_event.is_set():
        try:
            # Sniff for CAN packets with ID 0x618
            packets = can_socket.sniff(timeout=0.5, count=10, 
                                      lfilter=lambda pkt: pkt.identifier == REQUEST_ID)
            
            if packets and len(packets) > 0:
                for pkt in packets:
                    if stop_event.is_set():
                        break
                        
                    data = bytes(pkt.data)
                    print(f"{BLUE}[>] Raw CAN: ID={pkt.identifier:03X}, Data={format_bytes(data)}{RESET}")
                    
                    # Check for specific patterns in the data
                    if len(data) >= 3:
                        # For diagnostic session control (10 03)
                        if data[0] == 0x02 and data[1] == 0x10 and data[2] == 0x03:
                            response_data = bytes([0x06, 0x50, 0x03, 0x13, 0x24, 0x56, 0x12])
                            print(f"{CYAN}[*] Detected diagnostic session control (extended){RESET}")
                            
                            # Create and send response
                            response = CAN(identifier=RESPONSE_ID, data=response_data)
                            can_socket.send(response)
                            print(f"{GREEN}[<] Sent: ID={RESPONSE_ID:03X}, Data={format_bytes(response_data)}{RESET}")
                            
                        # For security access (27 01)
                        elif data[0] == 0x02 and data[1] == 0x27 and data[2] == 0x01:
                            random_seed = generate_random_bytes(4)
                            response_data = bytes([0x06, 0x67, 0x02]) + random_seed
                            print(f"{CYAN}[*] Detected security access, sending seed: {format_bytes(random_seed)}{RESET}")
                            
                            # Create and send response
                            response = CAN(identifier=RESPONSE_ID, data=response_data)
                            can_socket.send(response)
                            print(f"{GREEN}[<] Sent: ID={RESPONSE_ID:03X}, Data={format_bytes(response_data)}{RESET}")
                            
                        # For diagnostic session control (10 01)
                        elif data[0] == 0x02 and data[1] == 0x10 and data[2] == 0x01:
                            response_data = bytes([0x06, 0x50, 0x01, 0x13, 0x24, 0x56, 0x12])
                            print(f"{CYAN}[*] Detected diagnostic session control (default){RESET}")
                            
                            # Create and send response
                            response = CAN(identifier=RESPONSE_ID, data=response_data)
                            can_socket.send(response)
                            print(f"{GREEN}[<] Sent: ID={RESPONSE_ID:03X}, Data={format_bytes(response_data)}{RESET}")
                            
        except Exception as e:
            print(f"{RED}[!] Error: {e}{RESET}")
            time.sleep(1)
    
    # Clean up
    can_socket.close()
    print(f"{GREEN}[+] CAN server stopped{RESET}")

if __name__ == "__main__":
    main()
