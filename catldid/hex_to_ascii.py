import json

def getData(filename):
    with open(filename) as f:
        content = json.load(f)
        return content[0][2]
    return None

def splitWithEmpty(data):
    data = data.upper()
    printdata = ' '.join(data[i:i+2] for i in range(0, len(data), 2))
    return printdata

diddata = getData('1002_dids.json')
for k,v in diddata.items():
    print('Data:',v)
    data = splitWithEmpty(v)
    print('ASCII:',end='')
    for each in data.split(' '):
        print(chr(int(each,16)),end='')
    print('\n')