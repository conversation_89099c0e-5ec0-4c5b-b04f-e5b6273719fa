import json

def hex_to_ascii(hex_string):
    # 移除可能的 'ff' 填充
    hex_string = hex_string.replace('ff', '')
    
    try:
        # 将十六进制字符串转换为字节，然后解码为ASCII
        bytes_data = bytes.fromhex(hex_string)
        return bytes_data.decode('ascii', errors='ignore')
    except Exception as e:
        return f"转换错误: {str(e)}"

with open('data/dids.json', 'r') as f:
    data = json.load(f)

for k, v in data[0][2].items():
    ascii_result = hex_to_ascii(v)
    print("原始十六进制:", v)
    print("ASCII结果:", ascii_result)
    print("值:", v)
    print("-" * 50)