import pickle
import matplotlib.pyplot as plt
import numpy as np

# 读取memory_map数据
with open('catl_memory_map.pkl', 'rb') as f:
    memory_map = pickle.load(f)

# 将地址排序并转换为numpy数组
addresses = np.array(sorted(list(memory_map)))

# 创建图表
plt.figure(figsize=(20, 3))  # 设置图表大小，宽度较大以便于查看

# 创建y轴数据（统一高度）
y = np.ones_like(addresses)

# 绘制内存访问点
plt.bar(addresses, y, width=256, color='blue', alpha=0.6)

# 设置x轴范围和格式
plt.xlim(0, 0xFFFFFFFF)
plt.gca().xaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'0x{int(x):08X}'))

# 设置标题和标签
plt.title('Memory Access Distribution')
plt.xlabel('Memory Address')
plt.ylabel('Access')

# 调整x轴标签的角度，使其更易读
plt.xticks(rotation=45)

# 添加网格便于查看
plt.grid(True, alpha=0.3)

# 调整布局，确保标签完全显示
plt.tight_layout()

# 保存图表
plt.savefig('memory_distribution.png', dpi=300, bbox_inches='tight')
plt.close()

# 打印一些统计信息
total_addresses = len(memory_map)
address_ranges = []
current_start = None
current_end = None

# 计算连续的地址范围
for addr in sorted(memory_map):
    if current_start is None:
        current_start = addr
        current_end = addr
    elif addr == current_end + 1:
        current_end = addr
    else:
        address_ranges.append((current_start, current_end))
        current_start = addr
        current_end = addr

if current_start is not None:
    address_ranges.append((current_start, current_end))

print(f"\nMemory Access Statistics:")
print(f"Total unique addresses accessed: {total_addresses}")
print(f"\nContinuous Memory Ranges:")
for start, end in address_ranges:
    print(f"0x{start:08X} - 0x{end:08X} (Size: {end - start + 1} bytes)") 