#!/usr/bin/env python3
"""
CAN Server Program
Listens on CAN0 interface and responds to specific messages using ISO-TP protocol.
"""

import os
import signal
import random
import binascii
import sys
import time
from scapy.contrib.isotp import ISOTPSocket
from scapy.contrib.cansocket import CANSocket
from scapy.layers.can import CAN
from scapy.contrib.isotp import ISOTP
from scapy.all import raw

# Define colors for terminal output
RED = "\033[1;31m"
GREEN = "\033[1;32m"
YELLOW = "\033[1;33m"
BLUE = "\033[1;34m"
CYAN = "\033[1;36m"
RESET = "\033[0m"

# Define CAN IDs
REQUEST_ID = 0x618
RESPONSE_ID = 0x718

# Flag to control the main loop
running = True

def signal_handler(sig, frame):
    """Handle Ctrl+C to gracefully exit the program"""
    global running
    print(f"\n{YELLOW}[*] Stopping CAN server...{RESET}")
    running = False

def setup_isotp_socket():
    """Setup and check CAN interface and create ISO-TP socket"""
    try:

        # Create a CAN socket
        can_socket = CANSocket(channel='can0')

        # Create ISO-TP socket with proper addressing
        isotp_socket = ISOTPSocket(can_socket,
                                  tx_id=RESPONSE_ID,  # Our transmit ID (for responses)
                                  rx_id=REQUEST_ID,   # Our receive ID (for requests)
                                  padding=True)       # Use padding for ISO-TP frames

        print(f"{GREEN}[+] Successfully connected to CAN0 interface with ISO-TP protocol{RESET}")
        return isotp_socket
    except Exception as e:
        print(f"{RED}[!] Error setting up ISO-TP socket: {e}{RESET}")
        sys.exit(1)

def generate_random_bytes(length=4):
    """Generate random bytes for the response"""
    return bytes([random.randint(0, 255) for _ in range(length)])

def format_bytes(data):
    """Format bytes for display"""
    if isinstance(data, bytes):
        return ' '.join([f"{b:02X}" for b in data])
    else:
        return ' '.join([f"{b:02X}" for b in raw(data)])

def process_message(packet):
    """Process received ISO-TP message and generate response"""
    # Convert packet to raw bytes if it's a Scapy packet
    if hasattr(packet, 'load'):
        data = bytes(packet.load)
    else:
        data = bytes(packet)

    # Print received message
    print(f"{BLUE}[>] Received: ID={REQUEST_ID:03X}, Data={format_bytes(data)}{RESET}")

    # Debug: Print hex representation of each byte
    print(f"{YELLOW}[DEBUG] Raw bytes: {' '.join([f'{b:02X}' for b in data])}{RESET}")

    # Check message type and prepare response based on the content
    # For diagnostic session control (10 03)
    if len(data) >= 2 and data[0] == 0x10 and data[1] == 0x03:
        # Response for 10 03 (extended session)
        response_data = b'\x50\x03\x13\x24\x56\x12'
        print(f"{CYAN}[*] Detected diagnostic session control (extended){RESET}")

    # For security access (27 01)
    elif len(data) >= 2 and data[0] == 0x27 and data[1] == 0x01:
        # Response for 27 01 with random seed
        random_seed = generate_random_bytes(4)
        # Format should be 67 02 + 4 random bytes as specified
        response_data = b'\x67\x01' + random_seed
        seed_hex = format_bytes(random_seed)
        print(f"{CYAN}[*] Detected security access request, sending seed: {seed_hex}{RESET}")

    # For diagnostic session control (10 01)
    elif len(data) >= 2 and data[0] == 0x10 and data[1] == 0x01:
        # Response for 10 01 (default session)
        response_data = b'\x50\x01\x13\x24\x56\x12'
        print(f"{CYAN}[*] Detected diagnostic session control (default){RESET}")

    else:
        # No matching pattern
        print(f"{YELLOW}[*] No response pattern for this message{RESET}")
        return None

    print(f"{GREEN}[<] Sending: ID={RESPONSE_ID:03X}, Data={format_bytes(response_data)}{RESET}")
    return response_data

def main():
    """Main function to run the CAN server"""
    print(f"{CYAN}=== CAN Server Program (ISO-TP) ==={RESET}")
    print(f"{CYAN}Listening on CAN0 interface{RESET}")
    print(f"{CYAN}Monitoring for ID: 0x{REQUEST_ID:03X}{RESET}")
    print(f"{CYAN}Responding with ID: 0x{RESPONSE_ID:03X}{RESET}")
    print(f"{YELLOW}Press Ctrl+C to exit{RESET}")

    # Register signal handler for Ctrl+C
    signal.signal(signal.SIGINT, signal_handler)

    # Setup ISO-TP socket
    isotp_socket = setup_isotp_socket()

    # Alternative approach: Use a raw CAN socket to monitor traffic
    can_socket = CANSocket(channel='can0')

    # Main loop
    while running:
        try:
            # First try with ISO-TP socket
            try:
                packets = isotp_socket.sniff(timeout=0.5, count=1)

                if packets and len(packets) > 0:
                    packet = packets[0]
                    print(f"{GREEN}[+] Received ISO-TP packet{RESET}")

                    # Process message and get response
                    response = process_message(packet)

                    # Send response if available
                    if response:
                        isotp_socket.send(response)
                        continue
            except Exception as e:
                print(f"{YELLOW}[!] ISO-TP sniff error: {e}{RESET}")

            # If ISO-TP didn't work, try with raw CAN socket
            try:
                can_packets = can_socket.sniff(timeout=0.5, count=10,
                                              lfilter=lambda pkt: pkt.identifier == REQUEST_ID)

                if can_packets and len(can_packets) > 0:
                    print(f"{GREEN}[+] Received {len(can_packets)} raw CAN packets{RESET}")

                    # Process the first packet that matches our criteria
                    for can_pkt in can_packets:
                        if can_pkt.identifier == REQUEST_ID:
                            print(f"{BLUE}[>] Raw CAN: ID={can_pkt.identifier:03X}, Data={format_bytes(can_pkt.data)}{RESET}")

                            # Extract data from CAN packet
                            data = bytes(can_pkt.data)

                            # Check for diagnostic session control (10 03)
                            if len(data) >= 3 and data[1] == 0x10 and data[2] == 0x03:
                                response_data = b'\x50\x03\x13\x24\x56\x12'
                                print(f"{CYAN}[*] Detected diagnostic session control (extended) from raw CAN{RESET}")

                                # Create response CAN packet
                                response_can = CAN(identifier=RESPONSE_ID, data=b'\x06' + response_data)
                                can_socket.send(response_can)
                                print(f"{GREEN}[<] Sent raw CAN response{RESET}")

                            # Check for security access (27 01)
                            elif len(data) >= 3 and data[1] == 0x27 and data[2] == 0x01:
                                random_seed = generate_random_bytes(4)
                                response_data = b'\x67\x01' + random_seed
                                seed_hex = format_bytes(random_seed)
                                print(f"{CYAN}[*] Detected security access from raw CAN, sending seed: {seed_hex}{RESET}")

                                # Create response CAN packet
                                response_can = CAN(identifier=RESPONSE_ID, data=b'\x06' + response_data)
                                can_socket.send(response_can)
                                print(f"{GREEN}[<] Sent raw CAN response{RESET}")

                            # Check for diagnostic session control (10 01)
                            elif len(data) >= 3 and data[1] == 0x10 and data[2] == 0x01:
                                response_data = b'\x50\x01\x13\x24\x56\x12'
                                print(f"{CYAN}[*] Detected diagnostic session control (default) from raw CAN{RESET}")

                                # Create response CAN packet
                                response_can = CAN(identifier=RESPONSE_ID, data=b'\x06' + response_data)
                                can_socket.send(response_can)
                                print(f"{GREEN}[<] Sent raw CAN response{RESET}")
            except Exception as e:
                print(f"{RED}[!] Raw CAN error: {e}{RESET}")

        except Exception as e:
            print(f"{RED}[!] Unexpected error: {e}{RESET}")
            time.sleep(1)  # Wait before retrying

    # Clean up
    isotp_socket.close()
    can_socket.close()
    print(f"{GREEN}[+] CAN server stopped{RESET}")

if __name__ == "__main__":
    main()