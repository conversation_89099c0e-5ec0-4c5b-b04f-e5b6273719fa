import cmd2
import sys
import os
from utils.scanUDSid import *
import getopt
import logging
import binascii
from scapy.contrib.cansocket import CANSocket
from scapy.config import conf
import json
import time
from scapy.layers.can import CAN
from scapy.contrib.isotp import ISOTPSocket
from scapy.contrib.automotive.uds_scan import * 
from scapy.layers.can import *
from scapy.main import load_contrib
from reprint import output
conf.contribs['ISOTP'] = {'use-can-isotp-kernel-module': True}
conf.contribs['CANSocket'] = {'use-python-can': True}
load_contrib('isotp')
load_contrib('automotive.uds')
import time
import warnings


with warnings.catch_warnings():
    warnings.simplefilter("error", category=UserWarning)
    isoTpSocket = ISOTPSocket(CANSocket(bustype='socketcan', channel="can0", fd=False,
                                        can_filters=[{'can_id': 0x612, 'can_mask': 0x612 ^ 0x692 ^ 0x7ff}]),
                                tx_id=0x612, rx_id=0x692, padding=True, basecls=ISOTP)

sendData = binascii.a2b_hex('22f1ff')
isoTpMessage = ISOTP(sendData)
data = isoTpSocket.sr1(isoTpMessage,timeout = 0.1)
data = isoTpSocket.sniff(timeout=0.3)
print(data)
print(data)